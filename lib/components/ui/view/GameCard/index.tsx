'use client'
import React, { useState } from 'react';
import {Link} from '@i18n/navigation';
import Image from 'next/image';
import { Star } from 'lucide-react';
import { GameLocaleContent } from '@/lib/types';
import ClientImage from '../ClientImage';

interface GameCardProps extends GameLocaleContent {
  isPrimary?: boolean
}

export const GameCard: React.FC<GameCardProps> = (game) => {
  // 确定游戏链接路径
  const getGameHref = () => {
    // 检查是否为主页游戏
    if (game.isPrimary) {
      return "/"
    }
    // 其他游戏使用 /games/{slug} 路径，移除可能的 /games/ 前缀
    const cleanSlug = game.slug.replace(/^\/games\//, '')
    return `/games/${cleanSlug}`
  }

  // Map for category background colors
  const categoryColorMap = {
    indigo: 'bg-primary/10 text-primary',
    red: 'bg-destructive/10 text-destructive',
    green: 'bg-secondary/10 text-secondary',
    blue: 'bg-accent/10 text-accent-foreground',
    yellow: 'bg-muted text-muted-foreground',
    purple: 'bg-primary/20 text-primary',
    orange: 'bg-destructive/20 text-destructive',
  };

  // Map for badge colors
  const badgeColorMap = {
    red: 'bg-destructive text-destructive-foreground',
    green: 'bg-secondary text-secondary-foreground',
    blue: 'bg-primary text-primary-foreground',
    yellow: 'bg-muted text-muted-foreground',
    purple: 'bg-primary/80 text-primary-foreground',
    orange: 'bg-destructive/80 text-destructive-foreground',
  };

  // Generate stars based on rating
  const renderStars = () => {
    const stars = [];
    if (!game.gameInfo?.settings?.rating) {
      return null;
    }
    const fullStars = Math.floor(game.gameInfo?.settings?.rating);
    const hasHalfStar = game.gameInfo?.settings?.rating % 1 >= 0.5;

    for (let i = 1; i <= 5; i++) {
      if (i <= fullStars) {
        stars.push(<Star key={i} className="h-3 w-3 fill-current" />);
      } else if (i === fullStars + 1 && hasHalfStar) {
        stars.push(<Star key={i} className="h-3 w-3 fill-current" style={{ clipPath: 'inset(0 50% 0 0)' }} />);
      } else {
        stars.push(<Star key={i} className="h-3 w-3" />);
      }
    }

    return stars;
  };

  const [isHovered, setIsHovered] = useState(false);

  return (
    <Link
      href={getGameHref()}
      className="game-card bg-card rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative">
        <ClientImage
          src={game.gameImages?.[0] || ''}
          alt={game.gameName}
          width={200}
          height={112}
          className={`w-full h-28 object-cover transition-transform duration-300 ${isHovered ? 'scale-105' : ''}`}
        />
      </div>
      <div className="p-2">
        <h3 className={`font-bold text-card-foreground text-sm mb-1 transition-colors duration-300 ${isHovered ? 'text-primary' : ''}`}>{game.gameName}</h3>
        <div className="flex items-center text-yellow-500">
          {renderStars()}
        </div>
      </div>
    </Link>
  );
};

export default GameCard;
