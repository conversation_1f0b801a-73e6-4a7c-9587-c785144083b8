// lib/components/view/RightSidebar.tsx
"use client"

import React, { useMemo } from "react"
import { ChevronRight, Menu } from "lucide-react"
import { Link } from "@lib/i18n"
import ClientImage from "./ClientImage"
import { useTranslations } from "next-intl"
import { ProjectGame } from "@/lib/types"
import { getGameLocaleContent } from "@/lib/services/api-client"
import dayjs from "dayjs"
import { Button } from "@/lib/components/ui/button"
import {
	Sheet,
	SheetContent,
	SheetHeader,
	SheetTitle,
	SheetTrigger,
} from "@/lib/components/ui/sheet"
import { cn } from "@/lib/utils/react/styles"

interface RightSidebarProps {
	locale: string
	allGames?: ProjectGame[] // 所有游戏信息，用于获取关联游戏的详细信息
}

export const RightSidebar: React.FC<RightSidebarProps> = ({
	locale,
	allGames = [],
}) => {
	const t = useTranslations("Game")

	// 处理热门游戏（随机选择最多10个游戏）
	const hotGames = useMemo(() => {
		if (allGames.length < 5) return []

		// 复制数组并随机打乱
		const shuffled = [...allGames].sort(() => 0.5 - Math.random())
		return shuffled.slice(0, 5)
	}, [allGames])
	console.log("hotgames =====", hotGames.length)

	// 处理最新游戏（按更新时间倒序排列）
	const latestGames = useMemo(() => {
		return [...allGames]
			.sort((a, b) => {
				// 假设游戏信息中有更新时间字段，如果没有可以根据实际情况调整
				const timeA = dayjs(a.updateTime || new Date())
				const timeB = dayjs(b.updateTime || new Date())
				return timeB.diff(timeA)
			})
			.slice(0, 10)
	}, [allGames])

	// 渲染游戏卡片的函数 - 支持移动端响应式
	const renderGameCard = (game: ProjectGame, isMobile = false) => {
		// 获取图片URL
		const imageUrl = game.gameImages?.[0]
		const gameContent = getGameLocaleContent(locale, game)
		return (
			<Link
				href={game.slug}
				key={game.id}
				className={cn(
					"group relative overflow-hidden rounded-lg transition-all duration-300 hover:shadow-md flex flex-col touch-manipulation",
					isMobile ? "min-h-[120px]" : "",
				)}
			>
				{/* 使用固定高度的容器替代aspect-ratio - 移动端增大高度 */}
				<div
					className={cn(
						"overflow-hidden rounded-lg",
						isMobile ? "h-24 sm:h-20" : "h-20",
					)}
				>
					<ClientImage
						src={imageUrl || ""}
						alt={gameContent.gameName}
						className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
						fallbackSrc="/placeholder-image.png"
					/>
				</div>
				{/* 游戏标题 - 紧贴图片，移动端优化 */}
				<div
					className={cn("py-1 px-1 text-center mt-0", isMobile ? "py-2" : "")}
				>
					<span
						className={cn(
							"font-medium text-gray-700 dark:text-gray-300 line-clamp-1",
							isMobile ? "text-sm" : "text-xs",
						)}
						title={gameContent.gameName}
					>
						{gameContent.gameName}
					</span>
				</div>
				{/* 悬停效果覆盖层 */}
				<div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-2">
					<span
						className={cn(
							"font-medium text-white truncate",
							isMobile ? "text-base" : "text-sm",
						)}
					>
						{gameContent.gameName}
					</span>
				</div>
			</Link>
		)
	}

	// 渲染游戏列表的函数（带广告插入）- 支持移动端
	const renderGameList = (games: ProjectGame[], isMobile = false) => {
		if (!games || games.length === 0) return null

		const result = []

		// 每三行插入一个广告
		for (let i = 0; i < games.length; i++) {
			// 每6个游戏（3行）后插入一个广告
			if (i > 0 && i % 6 === 0) {
				result.push(
					<div
						key={`ad-${i / 6}`}
						className={cn("my-2", isMobile ? "col-span-1" : "col-span-2")}
					>
						<div
							className={cn(
								"w-full overflow-hidden rounded-lg bg-gray-200 flex items-center justify-center text-gray-500 hover:bg-gray-300 transition-all duration-300",
								isMobile
									? "aspect-square text-sm p-4"
									: "aspect-w-16 aspect-h-9 text-xs",
							)}
						>
							广告位
						</div>
					</div>,
				)
			}
			result.push(renderGameCard(games[i] as ProjectGame, isMobile))
		}

		return result
	}

	// 侧边栏内容组件 - 可复用于桌面端和移动端
	const SidebarContent = ({ isMobile = false }: { isMobile?: boolean }) => (
		<div className={cn("space-y-6", isMobile ? "p-4" : "")}>
			{/* 1. 广告位 */}
			<div className="rounded-lg bg-card p-4 shadow-sm overflow-hidden">
				<div
					className={cn(
						"w-full overflow-hidden rounded-lg bg-gray-200 flex items-center justify-center text-gray-500 hover:bg-gray-300 transition-all duration-300",
						isMobile
							? "aspect-square text-sm"
							: "aspect-w-16 aspect-h-9 text-xs",
					)}
				>
					广告位
				</div>
			</div>

			{/* 2. 热门游戏 */}
			{hotGames.length > 0 && (
				<div className="rounded-lg bg-card p-4 shadow-sm overflow-hidden">
					<h3
						className={cn(
							"mb-4 font-semibold text-card-foreground border-b pb-2 border-border flex items-center",
							isMobile ? "text-xl" : "text-lg",
						)}
					>
						<span className="inline-block w-1 h-5 bg-primary rounded-full mr-2"></span>
						{t("hotGamesTitle") || "热门游戏"}
					</h3>
					<div
						className={cn(
							"grid gap-4",
							isMobile ? "grid-cols-1 sm:grid-cols-2" : "grid-cols-2",
						)}
					>
						{renderGameList(hotGames, isMobile)}
					</div>
					<div className="mt-4 text-center">
						<Link
							href="/games"
							className={cn(
								"inline-flex items-center text-primary hover:text-primary/80 font-medium group",
								isMobile ? "text-base" : "text-sm",
							)}
						>
							{t("viewMoreGames") || "查看更多游戏"}
							<ChevronRight
								className={cn(
									"ml-1 transform group-hover:translate-x-1 transition-transform duration-300",
									isMobile ? "h-5 w-5" : "h-4 w-4",
								)}
							/>
						</Link>
					</div>
				</div>
			)}

			{/* 3. 最新游戏 */}
			{latestGames.length > 0 && (
				<div className="rounded-lg bg-card p-4 shadow-sm overflow-hidden">
					<h3
						className={cn(
							"mb-4 font-semibold text-card-foreground border-b pb-2 border-border flex items-center",
							isMobile ? "text-xl" : "text-lg",
						)}
					>
						<span className="inline-block w-1 h-5 bg-primary rounded-full mr-2"></span>
						{t("latestGamesTitle") || "最新游戏"}
					</h3>
					<div
						className={cn(
							"grid gap-4",
							isMobile ? "grid-cols-1 sm:grid-cols-2" : "grid-cols-2",
						)}
					>
						{renderGameList(latestGames, isMobile)}
					</div>
					<div className="mt-4 text-center">
						<Link
							href="/games"
							className={cn(
								"inline-flex items-center text-primary hover:text-primary/80 font-medium group",
								isMobile ? "text-base" : "text-sm",
							)}
						>
							{t("viewMoreGames") || "查看更多游戏"}
							<ChevronRight
								className={cn(
									"ml-1 transform group-hover:translate-x-1 transition-transform duration-300",
									isMobile ? "h-5 w-5" : "h-4 w-4",
								)}
							/>
						</Link>
					</div>
				</div>
			)}
		</div>
	)

	return (
		<>
			{/* 桌面端侧边栏 - 保持原有样式 */}
			<aside className="lg:top-24 lg:self-start h-fit space-y-6 hidden lg:block">
				<SidebarContent />
			</aside>

			{/* 移动端浮动按钮和抽屉 */}
			<div className="lg:hidden">
				<Sheet>
					<SheetTrigger asChild>
						<Button
							variant="outline"
							size="icon"
							className="fixed bottom-4 right-4 z-50 h-12 w-12 rounded-full shadow-lg bg-primary text-primary-foreground hover:bg-primary/90 border-0"
							aria-label="打开游戏推荐"
						>
							<Menu className="h-5 w-5" />
						</Button>
					</SheetTrigger>
					<SheetContent
						side="right"
						className="w-full sm:max-w-md overflow-y-auto"
					>
						<SheetHeader>
							<SheetTitle className="text-left">游戏推荐</SheetTitle>
						</SheetHeader>
						<SidebarContent isMobile={true} />
					</SheetContent>
				</Sheet>
			</div>
		</>
	)
}

export default RightSidebar
