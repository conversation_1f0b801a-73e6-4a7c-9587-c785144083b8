import { getRequestConfig } from "next-intl/server"
import cachedSiteSettings from "@/lib/config/siteSettings"
import { routing } from "./routing"
// 导入本地国际化文件
import zhTwMessages from "@/messages/zh-TW.json"
import enMessages from "@/messages/en.json"
import deMessages from "@/messages/de.json"
import frMessages from "@/messages/fr.json"
import jaMessages from "@/messages/ja.json"
import koMessages from "@/messages/ko.json"
import { hasLocale } from "next-intl"
// 获取站点配置中的语言设置
const defaultLocale = cachedSiteSettings.defaultLocale

// 本地国际化文件映射(必须包含工具当中定义的所有语言，这些属于模板当中直接使用到的国际化内容，不需要通过api获取)
const localMessages: Record<string, any> = {
	'zh-TW': zhTwMessages,
	'en': enMessages,
	'de': deMessages,
	'fr': frMessages,
	'ja': jaMessages,
	'ko': koMessages,
}

export default getRequestConfig(async ({ requestLocale }) => {
	const requested = await requestLocale;

	// 验证locale是否有效，如果无效则使用默认语言
	const finalLocale = hasLocale(routing.locales, requested)
	  ? requested
	  : routing.defaultLocale;

	try {
		// 加载请求的语言消息
		// 获取本地消息
		const messages = localMessages[finalLocale] || {};
		return {
			locale: finalLocale,
			messages,
			onError(error) {
				console.error("加载国际化内容时出现异常：")
				console.error(error)
			},
		}
	} catch (error) {
		console.error(
			`无法加载 ${finalLocale} 的翻译文件，将使用默认语言 ${defaultLocale}`,
		)

		// 加载默认语言消息作为备份
		const fallbackMessages = localMessages[defaultLocale] || {}

		return { locale: defaultLocale, messages: fallbackMessages }
	}
})
