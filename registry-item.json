{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "button", "type": "registry:ui", "title": "<PERSON><PERSON>", "description": "一个多功能按钮组件，支持各种样式变体、尺寸和状态", "files": [{"path": "lib/components/ui/button.tsx", "type": "registry:component"}], "dependencies": ["@radix-ui/react-slot", "class-variance-authority", "clsx", "tailwind-merge", "lucide-react"], "registryDependencies": [], "cssVars": {"theme": {"radius": "0.5rem"}, "light": {"button-background": "hsl(0 0% 98%)", "button-foreground": "hsl(240 10% 3.9%)", "button-border": "hsl(240 5.9% 90%)", "button-hover": "hsl(240 4.9% 93.9%)", "primary-background": "hsl(240 5.9% 10%)", "primary-foreground": "hsl(0 0% 98%)", "primary-hover": "hsl(240 3.7% 15.9%)", "secondary-background": "hsl(240 4.8% 95.9%)", "secondary-foreground": "hsl(240 5.9% 10%)", "secondary-hover": "hsl(240 5% 90%)", "destructive-background": "hsl(0 84.2% 60.2%)", "destructive-foreground": "hsl(0 0% 98%)", "destructive-hover": "hsl(0 64.9% 46.1%)"}, "dark": {"button-background": "hsl(240 10% 3.9%)", "button-foreground": "hsl(0 0% 98%)", "button-border": "hsl(240 3.7% 15.9%)", "button-hover": "hsl(240 3.7% 11%)", "primary-background": "hsl(0 0% 98%)", "primary-foreground": "hsl(240 5.9% 10%)", "primary-hover": "hsl(0 0% 80%)", "secondary-background": "hsl(240 3.7% 15.9%)", "secondary-foreground": "hsl(0 0% 98%)", "secondary-hover": "hsl(240 5.9% 24%)", "destructive-background": "hsl(0 84.2% 60.2%)", "destructive-foreground": "hsl(0 0% 98%)", "destructive-hover": "hsl(0 64.9% 46.1%)"}}, "docs": "一个功能强大的按钮组件，支持多种变体（默认、主要、次要、链接、Ghost和破坏性）和尺寸。它还集成了加载状态和图标支持。\n\n使用示例：\n```tsx\n<Button variant=\"primary\" size=\"lg\">\n  保存更改\n</Button>\n\n<Button variant=\"outline\" size=\"sm\" asChild>\n  <Link href=\"/docs\">文档</Link>\n</Button>\n\n<Button variant=\"destructive\" disabled>\n  删除项目\n</Button>\n\n<Button isLoading loadingText=\"处理中\">\n  提交\n</Button>\n```", "categories": ["ui", "basic", "forms", "feedback"], "meta": {"version": "1.0.0", "contributors": ["nexoui-team"]}}