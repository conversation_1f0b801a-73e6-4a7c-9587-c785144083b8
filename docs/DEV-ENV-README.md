# 开发环境管理

本项目提供了两种方式来启动按需开发环境：

1. 通过 Jenkins 自动化启动
2. 通过本地脚本手动启动

两种方式都支持：
- 根据项目ID启动独立的开发环境
- 自动分配端口
- 查看实时日志
- 15分钟后自动停止服务
- 通过公网IP访问内网开发环境

## Jenkins 自动化启动

### 配置 Jenkins 任务

1. 在 Jenkins 中创建一个新的流水线任务
2. 配置 Git 仓库源
3. 使用 `Jenkinsfile.dev` 作为流水线脚本文件
4. 添加字符串参数 `PROJECT_ID`

### 运行 Jenkins 任务

1. 点击"Build with Parameters"
2. 输入项目ID
3. 点击"Build"按钮

### 查看环境信息

构建完成后，Jenkins 控制台输出将显示：
- 本地服务器 URL
- 公网访问 URL
- 分配的端口
- 日志文件位置
- 进程 ID

### 自动关闭

开发环境将在启动后 15 分钟自动关闭。

## 本地脚本手动启动

项目根目录下的 `dev-env.sh` 脚本提供了本地管理开发环境的功能。

### 使用方法

```bash
# 启动开发环境
./dev-env.sh start project_id

# 查看环境状态
./dev-env.sh status [project_id]

# 查看日志
./dev-env.sh logs project_id

# 停止环境
./dev-env.sh stop project_id

# 停止项目的所有运行实例
./dev-env.sh stopall project_id
```

### 示例

```bash
# 启动项目ID为"project123"的开发环境
./dev-env.sh start project123

# 查看所有运行中的环境
./dev-env.sh status

# 查看特定项目的状态
./dev-env.sh status project123

# 查看日志
./dev-env.sh logs project123

# 停止环境
./dev-env.sh stop project123

# 停止项目的所有运行实例（包括可能未正常记录的实例）
./dev-env.sh stopall project123
```

### 自动关闭

开发环境将在启动后 15 分钟自动关闭。

## 公网访问

开发环境可以通过以下方式访问：

1. 内网访问: http://内网服务器IP:端口
2. 公网访问: http://公网服务器IP:端口

公网访问通过端口转发实现，支持端口范围：4001-4999。

注意：为避免端口冲突，Jenkins任务已设置最大并发构建数为999。

## 技术细节

### 端口分配

- Jenkins: 使用 `4001 + (BUILD_NUMBER % 999)` 分配端口（限制在4001-4999范围内）
- 本地脚本: 从 4001 开始查找第一个可用端口

### 环境变量

启动开发环境时会创建 `.env.local` 文件，包含以下配置：

```
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_DOMAIN="http://localhost:{port}"
NEXT_PUBLIC_USE_MOCK_DATA=true
NEXT_PUBLIC_PROJECT_ID="{project_id}"
```

### 日志文件

- Jenkins: 日志保存在工作区的 `dev-server-{BUILD_NUMBER}.log` 文件中
- 本地脚本: 日志保存在 `logs/{project_id}.log` 文件中

### 进程管理

- Jenkins: 进程 ID 保存在工作区的 `dev-server-{BUILD_NUMBER}.pid` 文件中
- 本地脚本: 进程 ID 保存在 `pids/{project_id}.pid` 文件中

### 停止运行实例

- 常规停止: `stop` 命令会停止通过 PID 文件记录的进程
- 强制停止: `stopall` 命令会查找并停止所有与指定项目 ID 相关的进程，即使它们没有正确记录在 PID 文件中

## 注意事项

1. 确保 Jenkins 服务器上已安装 Bun（或 Node.js）
2. 本地脚本优先使用 Bun，如果不可用则使用 npm
3. 开发环境需要访问互联网下载依赖
4. 如果需要更长的运行时间，可以修改超时设置：
   - Jenkins: 修改 `Jenkinsfile.dev` 中的 `TIMEOUT_SECONDS` 变量
   - 本地脚本: 修改 `dev-env.sh` 中的 `TIMEOUT_MINUTES` 变量
