import { MetadataRoute } from 'next';
// 添加静态生成配置
export const dynamic = 'force-static'
// 可选：添加重验证时间（如果需要的话）
export const revalidate = false
/**
 * 生成动态的 robots.txt
 */
export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_DOMAIN || '';
  
  return {
    rules: {
      userAgent: '*',
      allow: '/',
      disallow: [
        '/api/',
        '/_next/',
        '/public/',
        '/auth/',
      ],
    },
    sitemap: `${baseUrl}/sitemap.xml`,
  };
}
