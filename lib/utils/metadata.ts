import { Metada<PERSON> } from "next"
import { alternatesCanonical, generateHreflangData } from "@/lib/i18n/locales"
import { GameTemplateType, ProjectGame } from "../types"
import { GameType } from "../types/api-types"
import {
	getHomePageMetadata,
	getSiteSettings,
	getProjectGameMetadata,
  getGameLocaleContent,
} from "../services/api-client"

/**
 * 获取游戏页面的基础数据
 * @param pathstr 页面路径
 * @param projectGame 游戏数据
 * @param locale 语言
 * @returns 基础元数据和相关信息
 */
async function getGamePageBaseData(pathstr: string, projectGame: ProjectGame, locale = "en") {
	const path = pathstr ?? ""
	const siteConfig = await getSiteSettings()
	//  如果模板是盒子游戏，则使用首页全局配置，否则使用游戏作为元数据
	const metadataInfo =
		siteConfig.templateType === GameTemplateType.GameBox
			? await getHomePageMetadata(locale)
			: projectGame
				? await getProjectGameMetadata(locale, projectGame)
				: null

  const faqInfo =  getGameLocaleContent(locale, projectGame).contents.find(v => v.tabId === 'faq')

	// 获取 hreflang 数据
	const hreflangData = generateHreflangData(path)
	// 获取 canonical 数据
	const canonical = alternatesCanonical(locale, path)

	return {
		path,
		siteConfig,
		metadataInfo,
		hreflangData,
		canonical,
    faqInfo,
		gameLocale: projectGame.gameLocales?.find(gl => gl.locale === locale)?.content,
	}
}


/**
 * 增强游戏页的元数据
 * @param pathstr 页面路径
 * @param projectGame 游戏数据
 * @param locale 语言
 * @returns 增强的元数据
 */
/**
 * 生成游戏的结构化数据
 * @param pathstr 页面路径
 * @param projectGame 游戏数据
 * @param locale 语言
 * @returns 结构化数据对象
 */
export async function generateGameStructuredData(
	pathstr: string,
	projectGame: ProjectGame,
	locale = "en"
) {
	// 获取基础数据
	const { metadataInfo, canonical, gameLocale, faqInfo } = await getGamePageBaseData(pathstr, projectGame, locale)

	if (!metadataInfo) {
		console.warn(
			"当前游戏渲染时没有找到元数据"
		)
		return {
			"@context": "https://schema.org",
			"@type": "WebSite",
			name: projectGame?.name || "Game",
			url: "",
		}
	}

	// 如果有结构化数据，直接使用
	if (metadataInfo.structuredData) {
		console.log("使用结构化数据", metadataInfo.structuredData)
		return metadataInfo.structuredData
	}

	// 如果没有游戏数据，返回基本网站结构化数据
	if (!projectGame || !projectGame.id) {
		return {
			"@context": "https://schema.org",
			"@type": "WebSite",
			name: metadataInfo.title,
			url: canonical,
		}
	}

	// 构建游戏结构化数据
	const gameStructuredData = {
		"@context": "https://schema.org",
		"@type": "VideoGame",
		name: gameLocale?.gameName || projectGame.name,
		description: gameLocale?.gameDescription || metadataInfo.description,
		url: canonical,
		image: projectGame.gameImages?.[0],
		inLanguage: locale,
		// 添加游戏特定属性
		...(
			projectGame.gameInfo?.settings?.developer ? {
				author: {
					"@type": "Organization",
					name: projectGame.gameInfo.settings.developer
				}
			} : {}
		),
		...(
			projectGame.gameInfo?.settings?.releaseDate ? {
				datePublished: projectGame.gameInfo.settings.releaseDate
			} : {}
		),
		...(
			projectGame.gameInfo?.settings?.platform ? {
				gamePlatform: projectGame.gameInfo.settings.platform
			} : {}
		),
		...(
			projectGame.gameInfo?.settings?.version ? {
				gameEdition: projectGame.gameInfo.settings.version
			} : {}
		),
		...(
			projectGame.gameType === GameType.Download && projectGame.gameInfo?.gameDownload?.downloadUrls ? {
				offers: {
					"@type": "Offer",
					url: canonical,
					availability: "https://schema.org/InStock",
					price: "0",
					priceCurrency: "USD"
				}
			} : {}
		),
		...(
			projectGame.gameInfo?.settings?.ageRating ? {
				contentRating: projectGame.gameInfo.settings.ageRating
			} : {}
		),
		...(
			projectGame.tags && projectGame.tags.length > 0 ? {
				keywords: projectGame.tags.join(", ")
			} : {}
		),
		...(
			projectGame.categories && projectGame.categories.length > 0 ? {
				genre: projectGame.categories.join(", ")
			} : {}
		),
		...(
			projectGame.relatedVideos && projectGame.relatedVideos.length > 0 ? {
				trailer: {
					"@type": "VideoObject",
					url: projectGame.relatedVideos[0],
					thumbnailUrl: projectGame.gameImages?.[0],
					name: `${gameLocale?.gameName || projectGame.name}`,
					description: `${gameLocale?.gameName || projectGame.name}`
				}
			} : {}
		),
    // ...(faqInfo?.jsonContent ? {
    //   faq: [
    //     // @ts-ignore
    //     ...faqInfo.jsonContent.faqs.map((item: any) => ({
    //       question: item.question,
    //       answer: item.answer
    //     }))
    //   ]
    // }: {})
	}

	return gameStructuredData
}

/**
 * 增强游戏页的元数据
 * @param pathstr 页面路径
 * @param projectGame 游戏数据
 * @param locale 语言
 * @returns 增强的元数据
 */
export async function enhanceGameMetadata(
	pathstr: string,
	projectGame: ProjectGame,
	locale = "en",
): Promise<Metadata> {
	// 获取基础数据
	const { siteConfig, metadataInfo, hreflangData, canonical } = await getGamePageBaseData(pathstr, projectGame, locale)

	if (!metadataInfo) {
		console.warn(
			"当前首页渲染时没有找到元数据"
		)
		return { title: "MetadataInfo Not Found" }
	}

	const image = projectGame.gameImages?.[0]

	// 构建增强的元数据
	return {
		title: metadataInfo.title,
		description: metadataInfo.description,
		alternates: {
			languages: hreflangData.languages,
			canonical: canonical,
		},
		icons: {
			icon: siteConfig.icons?.favicon,
			apple: siteConfig.icons?.appleTouchIcon,
		},
		openGraph: {
			title: metadataInfo.ogTitle || metadataInfo.title,
			description: metadataInfo.ogDescription || metadataInfo.description,
			images: metadataInfo.ogImage
				? [{ url: metadataInfo.ogImage }]
				: image ? [{ url: image }] : undefined,
			url: canonical,
			type: "website",
			locale: locale,
		},
		twitter: {
			card: "summary_large_image",
			title: metadataInfo.title,
			description: metadataInfo.description,
			images: metadataInfo.twitterImage
				? [metadataInfo.twitterImage]
				: image ? [image] : undefined,
		}
	}
}
