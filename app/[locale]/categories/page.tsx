
import { Metadata } from "next"
import { setRequestLocale, getTranslations } from "next-intl/server"
import { locales as apiLocales } from "@/lib/i18n/locales"
import { Breadcrumb } from "@/lib/components/ui/view/Breadcrumb"
import { SidebarProvider } from "@/lib/components/ui/sidebar"
import {
	getGameCategoriesByLocale,
	getGames,
	getHomePageMetadata,
	getSiteSettings,
} from "@/lib/services/api-client"
import {
	BreadcrumbItem,
} from "@/lib/types/api-types"
import { GameTemplateType } from "@/lib/types"
import { Link } from "@/lib/i18n"
import { Icon } from "@/lib/components/common/Icon"

export const dynamic = 'force-static'

export async function generateStaticParams(): Promise<
	{ locale: string }[]
> {
	return apiLocales.map((locale) => ({ locale }))
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ locale: string }>
}): Promise<Metadata> {
	const { locale } = await params
	const t = await getTranslations({ locale, namespace: "Common" })
	const homeMetadata = await getHomePageMetadata(locale).catch(() => null)

	return {
		title: t("Category") || "Game Categories",
		description: "Explore all game categories and discover games by type.",
		openGraph: {
			title: t("Category") || "Game Categories",
			description: "Explore all game categories and discover games by type.",
			...(homeMetadata?.ogImage ? { images: [homeMetadata.ogImage] } : {}),
		},
	}
}

export default async function CategoriesOverviewPage({
	params,
}: {
	params: Promise<{ locale: string }>
}) {
	const { locale } = await params
	setRequestLocale(locale)

	const t = await getTranslations("Common")

	// 获取网站设置以检测模板类型
	const siteSettings = await getSiteSettings()
	const isGameBox = siteSettings.templateType === GameTemplateType.GameBox

	const categories = await getGameCategoriesByLocale(locale)
	const allGames = await getGames()

	// 计算每个分类下的游戏数量
	const categoriesWithCount = categories.map(category => {
		const gameCount = allGames.filter(game =>
			game.categories?.includes(category.code)
		).length
		return {
			...category,
			count: gameCount
		}
	}).filter(category => category.count > 0) // 只显示有游戏的分类

	const breadcrumbItems: BreadcrumbItem[] = [
		{ label: t("Home"), href: "/" },
		{ label: t("Category"), href: "/categories" },
	]

	const content = (
		<div className="container mx-auto px-4 py-8">
			<Breadcrumb items={breadcrumbItems} />

			<header className="mb-8 text-center">
				<h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
					{t("Category")}
				</h1>
				<p className="mt-2 max-w-2xl mx-auto text-base text-gray-500 dark:text-gray-400">
					Explore games by category
				</p>
			</header>

			<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
				{categoriesWithCount.map((category) => (
					<Link
						key={category.code}
						href={`/c/${category.slug}`}
						className="group bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
					>
						<div className="p-6">
							{category.icon && (
								<div className="mb-4">
									<Icon name={category.icon} size={24}></Icon>
								</div>
							)}
							<h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
								{category.name}
							</h3>
							{category.metadata?.description && (
								<p className="text-sm text-gray-500 dark:text-gray-400 mb-3 line-clamp-2">
									{category.metadata.description}
								</p>
							)}
							<div className="flex items-center justify-between">
								<span className="text-sm text-gray-500 dark:text-gray-400">
									{category.count} {category.count === 1 ? "game" : "games"}
								</span>
								<svg
									className="w-5 h-5 text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M9 5l7 7-7 7"
									/>
								</svg>
							</div>
						</div>
					</Link>
				))}
			</div>

			{categoriesWithCount.length === 0 && (
				<div className="text-center py-12">
					<p className="text-gray-500 dark:text-gray-400">
						No categories found
					</p>
				</div>
			)}
		</div>
	)

	// 只在GameBox模板下使用SidebarProvider
	return isGameBox ? (
		<SidebarProvider>{content}</SidebarProvider>
	) : (
		content
	)
}