/**
 * 构建前获取站点设置脚本
 * 该脚本在构建前执行，用于获取站点设置并保存到配置文件中
 * 目的是在运行和构建之前已经将基础配置信息保存到项目里面，减少远程API调用
 */

import fs from 'fs';
import path from 'path';
import { getSiteSettings } from '../lib/services/api-client';

// 配置文件保存路径
const CONFIG_FILE_PATH = path.resolve(process.cwd(), 'lib/config/siteSettings.ts');

/**
 * 将对象中的null值转换为undefined
 * 这样在生成TS文件时，null值会变为undefined，避免类型不匹配
 * @param obj 要处理的对象
 * @returns 处理后的对象
 */
function nullToUndefined(obj: any): any {
  if (obj === null) {
    return undefined;
  }
  
  if (typeof obj !== 'object') {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => nullToUndefined(item));
  }
  
  const result: Record<string, any> = {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      result[key] = nullToUndefined(obj[key]);
    }
  }
  
  return result;
}

/**
 * 自定义JSON序列化函数，将null转换为undefined
 * @param key 键
 * @param value 值
 * @returns 处理后的值
 */
function jsonReplacer(key: string, value: any): any {
  return value === null ? undefined : value;
}

/**
 * 主函数：获取站点设置并保存到配置文件
 */
async function fetchAndSaveSiteSettings() {
  try {
    console.log('开始获取站点设置...');
    
    // 获取环境变量
    const projectId = process.env.NEXT_PUBLIC_PROJECT_ID;
    if (!projectId) {
      throw new Error('环境变量 NEXT_PUBLIC_PROJECT_ID 未设置');
    }
    
    // 调用API获取站点设置，传入skipCache=true参数以跳过缓存
    const siteSettings = await getSiteSettings({ 
      projectId,
      skipCache: true 
    });
    
    if (!siteSettings) {
      throw new Error('获取站点设置失败');
    }
    
    console.log('成功获取站点设置');
    
    // 将null值转换为undefined
    const processedSettings = nullToUndefined(siteSettings);
    
    // 生成配置文件内容
    const fileContent = generateConfigFileContent(processedSettings);
    
    // 确保目录存在
    const dirPath = path.dirname(CONFIG_FILE_PATH);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    
    // 写入配置文件
    fs.writeFileSync(CONFIG_FILE_PATH, fileContent, 'utf8');
    
    console.log(`站点设置已保存到: ${CONFIG_FILE_PATH}`);
  } catch (error) {
    console.error('获取或保存站点设置时出错:', error);
    process.exit(1);
  }
}

/**
 * 生成配置文件内容
 * @param siteSettings 站点设置对象
 * @returns 配置文件内容字符串
 */
function generateConfigFileContent(siteSettings: any): string {
  // 使用自定义的replacer函数，将null转换为undefined
  const settingsJson = JSON.stringify(siteSettings, jsonReplacer, 2)
    // 将JSON中的null替换为undefined
    .replace(/"([^"]+)":\s*null/g, '"$1": undefined');
  
  return `/**
 * 站点设置配置文件
 * 该文件由构建脚本自动生成，请勿手动修改
 * 生成时间: ${new Date().toISOString()}
 */

import { SiteSettings } from '@/lib/types';

/**
 * 站点设置
 * 这些设置在构建时从API获取，并保存到此文件中
 * 在运行时可以直接使用，无需再次请求API
 */
export const siteSettings: SiteSettings = ${settingsJson};

export default siteSettings;
`;
}

// 执行主函数
fetchAndSaveSiteSettings();
