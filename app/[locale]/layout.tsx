import { cn } from "@/lib/utils/react"
import { NextIntlClientProvider } from "next-intl"
import { getMessages, setRequestLocale } from "next-intl/server"
import { alternatesLanguage } from "@lib/i18n"
import { Metadata } from "next"
import type { PropsWithChildren } from "react"
import { Providers } from "./providers"
import { DynamicHeader } from "@/lib/components/ui/view/DynamicHeader"
import { DynamicFooter } from "@/lib/components/ui/view/DynamicFooter"
import FontConfigGenerator from "@/lib/components/FontConfigGenerator"
import ThemeConfigGenerator from "@/lib/components/ThemeConfigGenerator"
import {
	getSiteSettings,
	getNavItems,
	getHomePageMetadata,
	getGameCategoriesByLocale,
} from "@/lib/services/api-client"
import { GoogleAnalytics, MicrosoftClarity } from "@/lib/components/analytics"
import Script from "next/script"
import { checkProperty } from "@/lib/utils/react"
import { GameTemplateType } from "@/lib/types"
import { GameBoxLayout } from "@/lib/components/layout/GameBoxLayout"
// 动态导入当前主题
import "./current-theme.css"
import "./font-theme.css"
import { isEmpty } from "radash"

type Props = PropsWithChildren<{ params: Promise<{ locale: string }> }>

export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { locale } = await params
	setRequestLocale(locale)
	const siteSettings = await getSiteSettings()
	const siteMetadata = await getHomePageMetadata(locale)
	checkProperty(siteSettings, "siteName", "请在网站设置中添加网站名称")
	checkProperty(siteMetadata, "title", "请在网站设置中添加网站标题")
	checkProperty(siteMetadata, "description", "请在网站设置中添加网站描述")

	const icons = siteSettings.icons

	return {
		title: siteMetadata?.title ?? siteSettings.siteName,
		description: siteMetadata?.description ?? siteSettings.siteName,
		alternates: {
			languages: alternatesLanguage(""),
		},
		icons: icons
			? {
					...(!isEmpty(icons?.favicon) ? { icon: icons?.favicon } : {}),
					...(!isEmpty(icons?.appleTouchIcon)
						? { apple: icons?.appleTouchIcon }
						: {}),
					...(!isEmpty(icons?.androidIcon)
						? { shortcut: icons?.androidIcon }
						: {}),
				}
			: undefined,
		robots: {
			index: false,
			follow: true,
		},
	}
}

export default async function LocaleLayout({ children, params }: Props) {
	const { locale } = await params

	setRequestLocale(locale)
	// 直接从第三方API获取Header、Footer和网站配置数据
	const siteSettings = await getSiteSettings()
	const siteMetadata = await getHomePageMetadata(locale)

	// 判断是否为开发环境
	const isDev = process.env.NODE_ENV === "development"

	// 在构建时生成字体配置文件
	await FontConfigGenerator({ siteSettings })

	// 在构建时生成主题配置文件
	await ThemeConfigGenerator({ siteSettings })
	const navItems = await getNavItems(locale)

	// 检查是否为GameBox模板
	const isGameBox = siteSettings.templateType === GameTemplateType.GameBox

	// 如果是GameBox模板，获取分类数据
	let categories: any[] = []
	if (isGameBox) {
		categories = await getGameCategoriesByLocale(locale)
	}

	return (
		<html suppressHydrationWarning lang={locale}>
			<head />
			<body
				className={cn(
					"min-h-screen font-sans antialiased",
					"bg-background dark:bg-tech-dark dark:text-white",
				)}
			>
				<NextIntlClientProvider locale={locale}>
					<Providers locale={locale}>
						{isGameBox ? (
							// GameBox模板布局：侧边栏 + 主内容区域（包含Header、内容、Footer）
							<GameBoxLayout categories={categories}>
								<DynamicHeader navItem={navItems} siteSettings={siteSettings} />
								{children}
								<DynamicFooter
									siteSettings={siteSettings}
									siteMetadata={siteMetadata}
								/>
							</GameBoxLayout>
						) : (
							// 普通模板布局：Header + 内容 + Footer
							<>
								<DynamicHeader navItem={navItems} siteSettings={siteSettings} />
								{children}
								<DynamicFooter
									siteSettings={siteSettings}
									siteMetadata={siteMetadata}
								/>
							</>
						)}

						{/* 统计代码，仅在非开发环境下加载 */}
						{!isDev && (
							<>
								{siteSettings.analytics?.gaId && (
									<GoogleAnalytics gaId={siteSettings.analytics.gaId} />
								)}
								{siteSettings.analytics?.plausible && (
									<Script
										defer
										data-domain={siteSettings.domain}
										src={siteSettings.analytics.plausible}
									/>
								)}
								{siteSettings.analytics?.clarityId && (
									<MicrosoftClarity
										clarityId={siteSettings.analytics.clarityId}
									/>
								)}
								{siteSettings.analytics?.adsenseClientId && (
									<Script
										async
										src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${siteSettings.analytics.adsenseClientId}`}
										crossOrigin="anonymous"
									/>
								)}
							</>
						)}
					</Providers>
				</NextIntlClientProvider>
			</body>
		</html>
	)
}
