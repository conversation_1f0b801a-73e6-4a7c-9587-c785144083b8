import { dash } from "radash"
/**
 * 图标映射对象，将图标名称映射到对应的组件
 */
import {
	// 常用图标
	Home,
	Settings,
	User,
	Mail,
	Search,
	Bell,
	File,
	Folder,
	Star,
	Heart,
	Plus,
	Minus,
	Check,
	X,
	Info,
	HelpCircle,
	AlertCircle,
	AlertTriangle,
	Calendar,
	Clock,
	// 方向图标
	ArrowUp,
	ArrowRight,
	ArrowDown,
	ArrowLeft,
	ChevronUp,
	ChevronRight,
	ChevronDown,
	ChevronLeft,
	ChevronsUp,
	ChevronsRight,
	ChevronsDown,
	ChevronsLeft,
	CornerUpLeft,
	CornerUpRight,
	CornerDownLeft,
	CornerDownRight,
	Move,
	Undo,
	Redo,
	// 媒体图标
	Image,
	Video,
	Music,
	Play,
	Pause,
	SkipBack,
	SkipForward,
	FastForward,
	Rewind,
	Volume,
	Volume1,
	Volume2,
	VolumeX,
	Mic,
	MicOff,
	Headphones,
	Camera,
	Film,
	Monitor,
	// 编辑图标
	Edit,
	Edit2,
	Edit3,
	Scissors,
	Copy,
	Clipboard,
	Trash,
	Trash2,
	Eraser,
	Pencil,
	Type,
	Underline,
	Italic,
	Bold,
	List,
	AlignLeft,
	AlignCenter,
	AlignRight,
	AlignJustify,
	// 界面图标
	Menu,
	Layout,
	Sidebar,
	Columns,
	Grid,
	Maximize,
	Minimize,
	Loader,
	RefreshCw,
	ExternalLink,
	Link,
	Upload,
	Download,
	LogIn,
	LogOut,
	ToggleLeft,
	ToggleRight,
	Sliders,
	// 社交图标
	Share,
	Share2,
	Send,
	MessageCircle,
	MessageSquare,
	Phone,
	Smartphone,
	Github,
	Gitlab,
	Twitter,
	Instagram,
	Facebook,
	Youtube,
	Linkedin,
	ThumbsUp,
	ThumbsDown,
	// 游戏图标
	Gamepad,
	Gamepad2,
	Dices,
	Ghost,
	Trophy,
	Medal,
	Flag,
	Target,
	Puzzle,
	PlayCircle,
	Joystick,
	// 其他图标
	Lightbulb,
	Users,
	FileText,
	Tag,
	Moon,
} from "lucide-react"

/**
 * 图标映射对象，将图标名称映射到对应的组件
 */
export const ICON_MAP: Record<string, React.ComponentType<any>> = {
	// 常用图标
	home: Home,
	settings: Settings,
	user: User,
	mail: Mail,
	search: Search,
	bell: Bell,
	file: File,
	folder: Folder,
	star: Star,
	heart: Heart,
	plus: Plus,
	minus: Minus,
	check: Check,
	tag: Tag,
	x: X,
	info: Info,
	helpCircle: HelpCircle,
	alertCircle: AlertCircle,
	alertTriangle: AlertTriangle,
	calendar: Calendar,
	clock: Clock,
	// 方向图标
	arrowUp: ArrowUp,
	arrowRight: ArrowRight,
	arrowDown: ArrowDown,
	arrowLeft: ArrowLeft,
	chevronUp: ChevronUp,
	chevronRight: ChevronRight,
	chevronDown: ChevronDown,
	chevronLeft: ChevronLeft,
	chevronsUp: ChevronsUp,
	chevronsRight: ChevronsRight,
	chevronsDown: ChevronsDown,
	chevronsLeft: ChevronsLeft,
	cornerUpLeft: CornerUpLeft,
	cornerUpRight: CornerUpRight,
	cornerDownLeft: CornerDownLeft,
	cornerDownRight: CornerDownRight,
	move: Move,
	undo: Undo,
	redo: Redo,
	// 媒体图标
	image: Image,
	video: Video,
	music: Music,
	play: Play,
	pause: Pause,
	skipBack: SkipBack,
	skipForward: SkipForward,
	fastForward: FastForward,
	rewind: Rewind,
	volume: Volume,
	volume1: Volume1,
	volume2: Volume2,
	volumeX: VolumeX,
	mic: Mic,
	micOff: MicOff,
	headphones: Headphones,
	camera: Camera,
	film: Film,
	monitor: Monitor,
	// 编辑图标
	edit: Edit,
	edit2: Edit2,
	edit3: Edit3,
	scissors: Scissors,
	copy: Copy,
	clipboard: Clipboard,
	trash: Trash,
	trash2: Trash2,
	eraser: Eraser,
	pencil: Pencil,
	type: Type,
	underline: Underline,
	italic: Italic,
	bold: Bold,
	list: List,
	alignLeft: AlignLeft,
	alignCenter: AlignCenter,
	alignRight: AlignRight,
	alignJustify: AlignJustify,
	// 界面图标
	menu: Menu,
	layout: Layout,
	sidebar: Sidebar,
	columns: Columns,
	grid: Grid,
	maximize: Maximize,
	minimize: Minimize,
	loader: Loader,
	refreshCw: RefreshCw,
	externalLink: ExternalLink,
	link: Link,
	upload: Upload,
	download: Download,
	logIn: LogIn,
	logOut: LogOut,
	toggleLeft: ToggleLeft,
	toggleRight: ToggleRight,
	sliders: Sliders,
	// 社交图标
	share: Share,
	share2: Share2,
	send: Send,
	messageCircle: MessageCircle,
	messageSquare: MessageSquare,
	phone: Phone,
	smartphone: Smartphone,
	github: Github,
	gitlab: Gitlab,
	twitter: Twitter,
	instagram: Instagram,
	facebook: Facebook,
	youtube: Youtube,
	linkedin: Linkedin,
	thumbsUp: ThumbsUp,
	thumbsDown: ThumbsDown,
	// 游戏图标
	gamepad: Gamepad,
	gamepad2: Gamepad2,
	dices: Dices,
	ghost: Ghost,
	trophy: Trophy,
	medal: Medal,
	flag: Flag,
	target: Target,
	puzzle: Puzzle,
	playCircle: PlayCircle,
	joystick: Joystick,
	// 其他图标
	"help-circle": HelpCircle, // 兼容连字符格式
	"refresh-cw": RefreshCw, // 兼容连字符格式
	lightbulb: Lightbulb,
	users: Users,
	"file-text": FileText,
	fileText: FileText,
	Moon,
}

// 添加兼容性映射，支持连字符格式的图标名称
Object.keys(ICON_MAP).forEach((key) => {
	ICON_MAP[dash(key) as string] = ICON_MAP[key]!
})

export function getIcon(name: string) {
	return ICON_MAP[dash(name)]
}
