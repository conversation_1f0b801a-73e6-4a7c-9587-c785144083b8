import { Metada<PERSON> } from "next"
import { setRequestLocale, getTranslations } from "next-intl/server"
import { locales as apiLocales } from "@/lib/i18n/locales"
import { Breadcrumb } from "@/lib/components/ui/view/Breadcrumb"
import {
	getGameTagsByLocale,
	getGames,
	getHomePageMetadata,
} from "@/lib/services/api-client"
import {
	BreadcrumbItem,
} from "@/lib/types/api-types"
import { Link } from "@/lib/i18n"

export const dynamic = 'force-static'

export async function generateStaticParams(): Promise<
	{ locale: string }[]
> {
	return apiLocales.map((locale) => ({ locale }))
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ locale: string }>
}): Promise<Metadata> {
	const { locale } = await params
	const t = await getTranslations({ locale, namespace: "Common" })
	const homeMetadata = await getHomePageMetadata(locale).catch(() => null)

	return {
		title: "Game Tags",
		description: "Explore all game tags and discover games by type.",
		openGraph: {
			title: "Game Tags",
			description: "Explore all game tags and discover games by type.",
			...(homeMetadata?.ogImage ? { images: [homeMetadata.ogImage] } : {}),
		},
	}
}

export default async function TagsOverviewPage({
	params,
}: {
	params: Promise<{ locale: string }>
}) {
	const { locale } = await params
	setRequestLocale(locale)

	const t = await getTranslations("Common")
	const tags = await getGameTagsByLocale(locale)
	const allGames = await getGames()

	// 计算每个标签下的游戏数量
	const tagsWithCount = tags.map(tag => {
		const gameCount = allGames.filter(game =>
			game.tags?.includes(tag.id)
		).length
		return {
			...tag,
			count: gameCount
		}
	}).filter(tag => tag.count > 0) // 只显示有游戏的标签

	const breadcrumbItems: BreadcrumbItem[] = [
		{ label: "Home", href: "/" },
		{ label: "Tags", href: "/tags" },
	]

	return (
		<div className="container mx-auto px-4 py-8">
			<Breadcrumb items={breadcrumbItems} />

			<header className="mb-8 text-center">
				<h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
					Tags
				</h1>
				<p className="mt-2 max-w-2xl mx-auto text-base text-gray-500 dark:text-gray-400">
					Explore games by tags
				</p>
			</header>

			<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
				{tagsWithCount.map((tag) => (
					<Link
						key={tag.id}
						href={`/tag/${tag.slug}`}
						className="group bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
					>
						<div className="p-6">
							{tag.imageUrl && (
								<div className="mb-4">
									<img
										src={tag.imageUrl}
										alt={tag.name}
										className="w-12 h-12 object-cover rounded-lg"
									/>
								</div>
							)}
							<h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
								{tag.name}
							</h3>
							{tag.description && (
								<p className="text-sm text-gray-500 dark:text-gray-400 mb-3 line-clamp-2">
									{tag.description}
								</p>
							)}
							<div className="flex items-center justify-between">
								<span className="text-sm text-gray-500 dark:text-gray-400">
									{tag.count} {tag.count === 1 ? "game" : "games"}
								</span>
								<svg
									className="w-5 h-5 text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M9 5l7 7-7 7"
									/>
								</svg>
							</div>
						</div>
					</Link>
				))}
			</div>

			{tagsWithCount.length === 0 && (
				<div className="text-center py-12">
					<p className="text-gray-500 dark:text-gray-400">
						No tags found
					</p>
				</div>
			)}
		</div>
	)
}
