import { SiteSettings } from '@/lib/types';
import { DEFAULT_FONTS, AVAILABLE_FONTS } from '@/lib/config/fonts';
import fs from 'fs';
import path from 'path';

interface FontConfigGeneratorProps {
  siteSettings: SiteSettings;
}

/**
 * 服务端字体配置生成器
 * 
 * 根据 siteConfig 中的字体配置，生成 font-theme.css 文件
 * 这个组件只在构建时运行，不会在客户端渲染
 */
export async function FontConfigGenerator({ siteSettings }: FontConfigGeneratorProps) {
  // 只在服务端运行
  if (typeof window !== 'undefined') {
    return null;
  }

  // 获取字体配置，如果没有则使用默认字体
  const fontConfig = siteSettings.fonts || {};
  
  // 确定最终使用的字体
  const sansFont = fontConfig.sans && AVAILABLE_FONTS[fontConfig.sans as keyof typeof AVAILABLE_FONTS]
    ? AVAILABLE_FONTS[fontConfig.sans as keyof typeof AVAILABLE_FONTS]
    : DEFAULT_FONTS.sans;
  
  const serifFont = fontConfig.serif && AVAILABLE_FONTS[fontConfig.serif as keyof typeof AVAILABLE_FONTS]
    ? AVAILABLE_FONTS[fontConfig.serif as keyof typeof AVAILABLE_FONTS]
    : DEFAULT_FONTS.serif;
  
  const monoFont = fontConfig.mono && AVAILABLE_FONTS[fontConfig.mono as keyof typeof AVAILABLE_FONTS]
    ? AVAILABLE_FONTS[fontConfig.mono as keyof typeof AVAILABLE_FONTS]
    : DEFAULT_FONTS.mono;

  // 生成CSS内容
  const cssContent = `@layer base {
  :root {
    --font-sans: ${sansFont};
    --font-serif: ${serifFont};
    --font-mono: ${monoFont};
  }
  
  .dark {
    --font-sans: ${sansFont};
    --font-serif: ${serifFont};
    --font-mono: ${monoFont};
  }
}`;

  try {
    // 在构建时生成 font-theme.css 文件
    const fontThemePath = path.join(process.cwd(), 'app/[locale]/font-theme.css');
    fs.writeFileSync(fontThemePath, cssContent);
    console.log('Font theme file generated:', fontThemePath);
  } catch (error) {
    console.error('Failed to generate font theme file:', error);
  }

  // 这个组件不渲染任何内容
  return null;
}

export default FontConfigGenerator;
