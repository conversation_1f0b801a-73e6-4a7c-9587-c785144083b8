import React from "react"
import { GamePageContent } from "./GamePageContent"
import {
	getProjectGame,
	getGameLocaleContent,
	getGames,
	getGameTagsByLocale,
} from "@/lib/services/api-client"
import {
	ProjectGame,
	GameLocaleContent,
	AdCode,
	GameTag,
} from "@/lib/types/api-types"

interface GameContentContainerProps {
	gameId: string
	locale: string
}

// 这个组件负责获取数据，然后将数据传递给 HomePageContent 组件
export async function GameContentContainer({
	gameId,
	locale,
}: GameContentContainerProps) {
	// 获取游戏信息
	const gameInfo: ProjectGame = await getProjectGame(gameId)

	// 获取游戏本地化内容
	const gameLocaleContent: GameLocaleContent = await getGameLocaleContent(
		locale,
		gameInfo,
	)

	// 获取所有游戏信息，用于获取关联游戏的详细信息
	const allGames = await getGames()

	// 获取广告代码 (假设有一个 getAdCodes API)
	// 这里使用模拟数据，后续可以替换为实际 API
	const adCodes: AdCode[] = [
		{ id: "1", position: "content", code: "<div>广告位</div>" },
		{ id: "2", position: "content-middle", code: "<div>内容中广告位</div>" },
		{ id: "3", position: "faq", code: "<div>FAQ广告位</div>" },
		{ id: "4", position: "sidebar", code: "<div>侧边栏广告位</div>" },
	]

	// 将获取的数据传递给 HomePageContent 组件
	return (
		<GamePageContent
			projectGame={gameInfo}
			gameLocaleContent={gameLocaleContent}
			adCodes={adCodes}
			allGames={allGames}
			locale={locale}
		/>
	)
}

export default GameContentContainer
