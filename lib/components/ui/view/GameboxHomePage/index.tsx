import { Breadcrumb } from "@/lib/components/ui/view/Breadcrumb"
import { GameCard } from "@/lib/components/ui/view/GameCard"

import { SidebarProvider } from "@/lib/components/ui/sidebar"
import {
	getGameCategoriesByLocale,
	getGames,
	getGameTagsByLocale
} from "@/lib/services/api-client"
import {
	BreadcrumbItem,
	GameLocaleContent
} from "@/lib/types"
import { getTranslations } from "next-intl/server"

interface GameboxHomePageProps {
	locale: string
}

// 服务端组件容器，负责数据获取
export async function GameboxHomePage({ locale }: GameboxHomePageProps) {
	// 获取翻译函数
	const t = await getTranslations()

	// 获取所有游戏数据
	const allGames = await getGames()

	// 获取游戏分类
	const categories = await getGameCategoriesByLocale(locale)

	// 获取游戏标签
	const tags = await getGameTagsByLocale(locale)

	// 处理游戏数据，转换为 GameLocaleContent 格式
	const processedGames: GameLocaleContent[] = allGames
		.map((game) => {
			const gameLocale = game.gameLocales?.find((gl) => gl.locale === locale)
			return gameLocale?.content
		})
		.filter(Boolean) as GameLocaleContent[]

	// 获取热门游戏（按评分排序，取前8个）
	const popularGames = processedGames
		.filter((game) => game.gameInfo?.settings?.rating)
		.sort((a, b) => (b.gameInfo?.settings?.rating || 0) - (a.gameInfo?.settings?.rating || 0))
		.slice(0, 8)

	// 获取最新游戏（按更新时间排序，取前8个）
	const latestGames = processedGames
		.filter((game) => game.updateTime)
		.sort((a, b) => new Date(b.updateTime!).getTime() - new Date(a.updateTime!).getTime())
		.slice(0, 8)

	// 按分类组织游戏
	const gamesByCategory = categories.map((category) => {
		const categoryGames = allGames
			.filter((game) => game.categories?.includes(category.code))
			.map((game) => {
				const gameLocale = game.gameLocales?.find((gl) => gl.locale === locale)
				return gameLocale?.content
			})
			.filter(Boolean)
			.slice(0, 6) as GameLocaleContent[] // 每个分类显示6个游戏

		return {
			category,
			games: categoryGames,
		}
	})

	// 面包屑导航
	const breadcrumbItems: BreadcrumbItem[] = [
		{ label: t("Common.Home"), href: "/" },
	]

	return (
		<SidebarProvider>

			<div className="space-y-8">
				{/* 面包屑导航 */}
				<Breadcrumb items={breadcrumbItems} />

				{/* 页面标题 */}
				<div className="text-center space-y-4">
					<h1 className="text-4xl font-bold text-foreground">
						{t("GameBox.title")}
					</h1>
					<p className="text-lg text-muted-foreground max-w-2xl mx-auto">
						{t("GameBox.description")}
					</p>
				</div>

				{/* 热门游戏区域 */}
				{popularGames.length > 0 && (
					<section className="space-y-4">
						<h2 className="text-2xl font-bold text-foreground flex items-center gap-2">
							🔥 {t("GameBox.popularGames")}
						</h2>
						<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
							{popularGames.map((game, index) => (
								<GameCard key={`popular-${index}`} {...game} />
							))}
						</div>
					</section>
				)}

				{/* 最新游戏区域 */}
				{latestGames.length > 0 && (
					<section className="space-y-4">
						<h2 className="text-2xl font-bold text-foreground flex items-center gap-2">
							✨ {t("GameBox.latestGames")}
						</h2>
						<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
							{latestGames.map((game, index) => (
								<GameCard key={`latest-${index}`} {...game} />
							))}
						</div>
					</section>
				)}

				{/* 按分类展示游戏 */}
				{gamesByCategory.map(({ category, games }) => (
					games.length > 0 && (
						<section key={category.code} className="space-y-4">
							<h2 className="text-2xl font-bold text-foreground flex items-center gap-2">
								{category.icon && (
									<img
										src={category.icon}
										alt={category.name}
										className="w-6 h-6"
									/>
								)}
								{category.name}
							</h2>
							<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
								{games.map((game, index) => (
									<GameCard key={`${category.code}-${index}`} {...game} />
								))}
							</div>
						</section>
					)
				))}

				{/* 如果没有游戏数据，显示空状态 */}
				{processedGames.length === 0 && (
					<div className="text-center py-12">
						<p className="text-lg text-muted-foreground">
							{t("GameBox.noGames")}
						</p>
					</div>
				)}
			</div>

		</SidebarProvider>
	)
}

export default GameboxHomePage
