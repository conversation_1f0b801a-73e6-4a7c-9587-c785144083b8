#!/bin/bash

# 端口转发设置脚本
# 在公网服务器上运行此脚本，设置从公网服务器到内网服务器的端口转发

# 配置
INTERNAL_SERVER_IP="内网服务器IP"  # 替换为你的内网服务器IP
PORT_RANGE_START=4001
PORT_RANGE_END=4999

# 检查是否以root权限运行
if [ "$(id -u)" != "0" ]; then
   echo "此脚本需要root权限运行"
   echo "请使用 sudo $0 运行"
   exit 1
fi

# 启用IP转发
echo "启用IP转发..."
echo 1 > /proc/sys/net/ipv4/ip_forward
sysctl -w net.ipv4.ip_forward=1

# 清除现有的端口转发规则
echo "清除现有的端口转发规则..."
iptables -t nat -F PREROUTING
iptables -t nat -F POSTROUTING

# 设置端口转发规则
echo "设置端口转发规则 ($PORT_RANGE_START-$PORT_RANGE_END)..."
for port in $(seq $PORT_RANGE_START $PORT_RANGE_END); do
    echo "设置端口 $port 转发到 $INTERNAL_SERVER_IP:$port"
    iptables -t nat -A PREROUTING -p tcp --dport $port -j DNAT --to-destination $INTERNAL_SERVER_IP:$port
done

# 设置MASQUERADE规则
echo "设置MASQUERADE规则..."
iptables -t nat -A POSTROUTING -j MASQUERADE

# 保存iptables规则
echo "保存iptables规则..."

# 检测Linux发行版并使用相应的方法保存规则
if [ -f /etc/debian_version ]; then
    # Debian/Ubuntu
    echo "检测到Debian/Ubuntu系统"

    # 安装iptables-persistent（如果尚未安装）
    if ! dpkg -l | grep -q iptables-persistent; then
        echo "安装iptables-persistent..."
        apt-get update
        apt-get install -y iptables-persistent
    fi

    # 保存规则
    iptables-save > /etc/iptables/rules.v4
    echo "规则已保存到 /etc/iptables/rules.v4"

elif [ -f /etc/redhat-release ]; then
    # CentOS/RHEL
    echo "检测到CentOS/RHEL系统"

    # 保存规则
    service iptables save
    echo "规则已通过service iptables save保存"

else
    # 其他Linux发行版
    echo "未能识别的Linux发行版"
    echo "手动保存iptables规则:"
    echo "iptables-save > /etc/iptables.rules"

    # 创建一个启动脚本来加载规则
    iptables-save > /etc/iptables.rules

    if [ ! -f /etc/network/if-pre-up.d/iptables ]; then
        echo "创建启动脚本 /etc/network/if-pre-up.d/iptables"
        cat > /etc/network/if-pre-up.d/iptables << EOF
#!/bin/sh
/sbin/iptables-restore < /etc/iptables.rules
EOF
        chmod +x /etc/network/if-pre-up.d/iptables
    fi
fi

echo "端口转发设置完成!"
echo "端口范围: $PORT_RANGE_START-$PORT_RANGE_END"
echo "内网服务器: $INTERNAL_SERVER_IP"
echo ""
echo "现在可以通过以下地址访问内网开发环境:"
echo "http://*************:端口号"
