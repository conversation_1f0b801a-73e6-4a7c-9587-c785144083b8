@import "tailwindcss";

@custom-variant dark (&:is(.dark *));
@plugin "@tailwindcss/typography";
@plugin "tailwind-scrollbar";

@theme inline {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);

	--font-sans: var(--font-sans);
	--font-mono: var(--font-mono);
	--font-serif: var(--font-serif);

	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);

	--shadow-2xs: var(--shadow-2xs);
	--shadow-xs: var(--shadow-xs);
	--shadow-sm: var(--shadow-sm);
	--shadow: var(--shadow);
	--shadow-md: var(--shadow-md);
	--shadow-lg: var(--shadow-lg);
	--shadow-xl: var(--shadow-xl);
	--shadow-2xl: var(--shadow-2xl);
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}
	body {
		@apply bg-background text-foreground;
	}
}

:root {
	--sidebar: hsl(0 0% 98%);
	--sidebar-foreground: hsl(240 5.3% 26.1%);
	--sidebar-primary: hsl(240 5.9% 10%);
	--sidebar-primary-foreground: hsl(0 0% 98%);
	--sidebar-accent: hsl(240 4.8% 95.9%);
	--sidebar-accent-foreground: hsl(240 5.9% 10%);
	--sidebar-border: hsl(220 13% 91%);
	--sidebar-ring: hsl(217.2 91.2% 59.8%);
}

.dark {
	--sidebar: hsl(240 5.9% 10%);
	--sidebar-foreground: hsl(240 4.8% 95.9%);
	--sidebar-primary: hsl(224.3 76.3% 48%);
	--sidebar-primary-foreground: hsl(0 0% 100%);
	--sidebar-accent: hsl(240 3.7% 15.9%);
	--sidebar-accent-foreground: hsl(240 4.8% 95.9%);
	--sidebar-border: hsl(240 3.7% 15.9%);
	--sidebar-ring: hsl(217.2 91.2% 59.8%);
}

/* 游戏网站特定样式 */
/* 粘性头部样式 */
.sticky-header {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

/* 移动菜单样式 */
.mobile-dropdown-content {
  transition: all 0.3s ease;
}

/* 标签页样式 */
.tab-pane {
  transition: opacity 0.3s ease;
}

/* 游戏卡片悬停效果 */
.game-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.game-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 全屏模式样式 */
:fullscreen {
  background-color: #000;
}


/* 响应式iframe容器 */
.aspect-w-16 {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 */
  max-height: calc(100vh - 220px);
}

.aspect-w-16 iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

@media (min-width: 768px) {
  .aspect-w-16.md\:aspect-h-7 {
    padding-bottom: 43.75%; /* 16:7 */
  }
}

/* 全屏模式样式 */
.fullscreen-mode {
  background-color: #000;
  z-index: 9999;
}

/* 下拉菜单动画 */
.group:hover .group-hover\:opacity-100 {
  transition-delay: 0.1s;
}

.group:hover .group-hover\:visible {
  transition-delay: 0.1s;
}

.group\/sub:hover .group-hover\/sub\:opacity-100 {
  transition-delay: 0.1s;
}

.group\/sub:hover .group-hover\/sub\:visible {
  transition-delay: 0.1s;
}

/* 移动菜单图标旋转动画 */
.mobile-dropdown-icon {
  transition: transform 0.3s ease;
}
