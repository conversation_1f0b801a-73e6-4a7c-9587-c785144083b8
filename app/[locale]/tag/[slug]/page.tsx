import React from "react"
import { <PERSON>ada<PERSON> } from "next"
import { Breadcrumb } from "@/lib/components/ui/view/Breadcrumb"
import { GameCard } from "@/lib/components/ui/view/GameCard"
import { SidebarProvider } from "@/lib/components/ui/sidebar"
import {
	getGameTagList,
	getGameTagsByLocale,
	getGameTagDetailBySlug,
	getGames,
	getHomePageMetadata,
	getDefaultLocaleByAPI,
	getSiteSettings,
} from "@/lib/services/api-client"
import { alternatesLanguage, locales as apiLocales, defaultLocale } from "@/lib/i18n/locales"
import { setRequestLocale, getTranslations } from "next-intl/server"
import { getGameLocaleContent } from "@/lib/services/api-client"
import { notFound } from "next/navigation"
import {
	GameTag,
	BreadcrumbItem,
	GameLocaleContent,
	GameTemplateType,
} from "@/lib/types"
export const dynamic = 'force-static';
type Props = {
	params: Promise<{ locale: string; slug: string }>
}

export async function generateStaticParams() {
	try {
		// Get all available tags from API (all languages)
		const tagList = await getGameTagList()
		const params = []
		if (!tagList || tagList.length === 0) {
			console.warn("generateStaticParams: No tags found, returning empty params.");
			for (const locale of apiLocales) {
				params.push({
					locale,
					slug: "index"
				})
			}
			return params
		}
		// 判断默认语言的标签是否为空，如果为空，则返回空
		const defaultLocaleTag = tagList.find((item) => item.locale === defaultLocale)
		if (!defaultLocaleTag || defaultLocaleTag.tags.length === 0) {
			console.warn("generateStaticParams: default locale No tags found, returning empty params.");
			for (const locale of apiLocales) {
				params.push({
					locale,
					slug: "index"
				})
			}
			return params
		}
		// Generate all possible combinations of locale and tag slug

		for (const locale of apiLocales) {
			// 查找当前语言的标签
			const localeData = tagList.find((item) => item.locale === locale)
			if (localeData?.tags) {
				for (const tag of localeData.tags) {
					params.push({
						locale,
						slug: tag.slug,
					})
				}
			}
		}

		return params
	} catch (error) {
		console.error("Failed to fetch params for generateStaticParams:", error)
		return []
	}
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { locale, slug } = await params
	if (!locale) {
		return { title: "Locale Not Found" }
	}

	setRequestLocale(locale)

	// Get metadata from API with tag slug
	const tag: GameTag = await getGameTagDetailBySlug(locale, slug)
	const languages = await alternatesLanguage(tag.slug)
	return {
		title: tag.metaTitle || `${slug} Games`,
		description: tag.metaDescription || `Browse all games tagged with ${slug}`,
		openGraph: {
			title: tag.metaTitle || `${slug} Games`,
			description:
				tag.metaDescription || `Browse all games tagged with ${slug}`,
			images: tag.imageUrl ? [{ url: tag.imageUrl }] : undefined,
			url: tag.slug,
		},
		twitter: {
			card: "summary" as
				| "summary"
				| "summary_large_image"
				| "app"
				| "player",
			title: tag.metaTitle || `${slug} Games`,
			description:
				tag.metaDescription || `Browse all games tagged with ${slug}`,
			images: tag.imageUrl ? [tag.imageUrl] : undefined,
		},
		alternates: {
			languages,
		},
	}
}

export default async function TagDetailPage({ params }: Props) {
	const { locale, slug } = await params

	setRequestLocale(locale)

	const t = await getTranslations("Common")

	// 获取网站设置以检测模板类型
	const siteSettings = await getSiteSettings()
	const isGameBox = siteSettings.templateType === GameTemplateType.GameBox

	// 获取标签详情
	const tag: GameTag = await getGameTagDetailBySlug(locale, slug)

	if (!tag || !tag.name) {
		notFound()
	}

	// 获取所有游戏
	const allGames = await getGames()

	// 筛选出包含当前标签的游戏
	const tagGames = allGames.filter((game) => game?.tags?.includes(tag.id))

	// 转换为本地化内容
	const gamesWithLocaleContent: GameLocaleContent[] = tagGames
		.map(game => {
			try {
				return getGameLocaleContent(locale, game)
			} catch (error) {
				console.warn(`Could not get locale content for game ${game.name}:`, error)
				return null
			}
		})
		.filter((game): game is GameLocaleContent => game !== null && !!game.id)

	// 获取面包屑数据
	const breadcrumbItems: BreadcrumbItem[] = [
		{ label: t("Home"), href: "/" },
		{ label: "Tags", href: "/tags" },
		{ label: tag.name, href: `/tag/${tag.slug}` },
	]

	const content = (
		<div className="container mx-auto px-4 py-8">
			<Breadcrumb items={breadcrumbItems} />

			<header className="mb-8 text-center">
				<h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
					{tag.name}
				</h1>
				{tag.description && (
					<p className="mt-2 max-w-2xl mx-auto text-base text-gray-500 dark:text-gray-400">
						{tag.description}
					</p>
				)}
				<p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
					{gamesWithLocaleContent.length} {gamesWithLocaleContent.length === 1 ? "game" : "games"} found
				</p>
			</header>

			{/* 游戏网格 */}
			{gamesWithLocaleContent.length > 0 ? (
				<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 mb-8">
					{gamesWithLocaleContent.map((game) => (
						<GameCard key={game.id} {...game} />
					))}
				</div>
			) : (
				<div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg mb-8">
					<p className="text-gray-500 dark:text-gray-400 text-lg">
						No games found with this tag
					</p>
				</div>
			)}
		</div>
	)

	// 只在GameBox模板下使用SidebarProvider
	return isGameBox ? (
		<SidebarProvider>{content}</SidebarProvider>
	) : (
		content
	)
}
