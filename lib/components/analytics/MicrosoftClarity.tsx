'use client';

import Script from 'next/script';

interface MicrosoftClarityProps {
  clarityId: string;
}

/**
 * Microsoft Clarity 组件
 * 用于在客户端加载 Microsoft Clarity 脚本
 */
export default function MicrosoftClarity({ clarityId }: MicrosoftClarityProps) {
  return (
    <Script
      id="microsoft-clarity"
      strategy="afterInteractive"
      dangerouslySetInnerHTML={{
        __html: `
          (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
          })(window, document, "clarity", "script", "${clarityId}");
        `,
      }}
    />
  );
}
