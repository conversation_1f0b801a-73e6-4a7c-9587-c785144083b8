{"$schema": "https://biomejs.dev/schemas/1.8.3/schema.json", "files": {"ignore": ["node_modules/**/*", ".next/**/*", ".turbo/**/*", ".vercel/**/*", ".wrangler/**/*", "scripts/**/*", "playwright-tests/**/*", ".windsurf<PERSON><PERSON>"]}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"useKeyWithClickEvents": "off", "useButtonType": "off", "useAltText": "off"}, "style": {"noUselessElse": "off", "noNonNullAssertion": "off", "useNodejsImportProtocol": "off", "useSelfClosingElements": "off", "useImportType": "off"}, "suspicious": {"noExplicitAny": "off", "noArrayIndexKey": "off", "noGlobalIsNan": "off"}, "security": {"noDangerouslySetInnerHtml": "off"}, "correctness": {"useExhaustiveDependencies": "off", "noSwitchDeclarations": "off"}, "complexity": {"noForEach": "off", "noStaticOnlyClass": "off"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "ignore": [], "attributePosition": "auto", "indentStyle": "tab", "indentWidth": 2, "lineWidth": 80, "lineEnding": "lf"}, "javascript": {"formatter": {"semicolons": "asNeeded"}}}