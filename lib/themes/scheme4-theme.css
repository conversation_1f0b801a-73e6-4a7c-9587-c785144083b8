@layer base {
	:root {
		--background: hsl(40, 10%, 97.84%);
		--foreground: hsl(40, 5%, 20%);
		--card: hsl(0 0% 100%);
		--card-foreground: hsl(40, 5%, 20%);
		--popover: hsl(0 0% 100%);
		--popover-foreground: hsl(40, 5%, 20%);
		--primary: hsl(38, 96%, 50%);
		--primary-foreground: hsl(0 0% 100%);
		--secondary: hsl(35, 92%, 44%);
		--secondary-foreground: hsl(0 0% 100%);
		--muted: hsl(40, 16%, 85%);
		--muted-foreground: hsl(40, 5%, 43.14%);
		--accent: hsl(38, 96%, 80%);
		--accent-foreground: hsl(40, 5%, 20%);
		--destructive: hsl(0 84.24% 60.2%);
		--destructive-foreground: hsl(0 0% 100%);
		--border: hsl(40, 5%, 83.14%);
		--input: hsl(40, 5%, 83.14%);
		--ring: hsl(38, 96%, 50%);
		--chart-1: hsl(38, 96%, 50%);
		--chart-2: hsl(35, 92%, 44%);
		--chart-3: hsl(45, 96%, 50%);
		--chart-4: hsl(30, 92%, 50%);
		--chart-5: hsl(25, 92%, 50%);
		--sidebar: hsl(40, 10%, 97.84%);
		--sidebar-foreground: hsl(40, 5%, 20%);
		--sidebar-primary: hsl(38, 96%, 50%);
		--sidebar-primary-foreground: hsl(0 0% 100%);
		--sidebar-accent: hsl(35, 92%, 44%);
		--sidebar-accent-foreground: hsl(40, 5%, 20%);
		--sidebar-border: hsl(40, 5%, 83.14%);
		--sidebar-ring: hsl(38, 96%, 50%);
	}

	.dark {
		--background: hsl(40, 14%, 12%);
		--foreground: hsl(40, 5%, 89.8%);
		--card: hsl(40, 7%, 19.8%);
		--card-foreground: hsl(40, 5%, 89.8%);
		--popover: hsl(40, 7%, 19.8%);
		--popover-foreground: hsl(40, 5%, 89.8%);
		--primary: hsl(38, 96%, 50%);
		--primary-foreground: hsl(0 0% 100%);
		--secondary: hsl(35, 92%, 44%);
		--secondary-foreground: hsl(40, 5%, 89.8%);
		--muted: hsl(40, 5%, 26.67%);
		--muted-foreground: hsl(40, 5%, 63.92%);
		--accent: hsl(38, 60%, 40%);
		--accent-foreground: hsl(40, 5%, 89.8%);
		--destructive: hsl(0 84.24% 60.2%);
		--destructive-foreground: hsl(0 0% 100%);
		--border: hsl(40, 5%, 26.67%);
		--input: hsl(40, 5%, 26.67%);
		--ring: hsl(38, 96%, 50%);
		--chart-1: hsl(38, 96%, 50%);
		--chart-2: hsl(35, 92%, 44%);
		--chart-3: hsl(45, 96%, 50%);
		--chart-4: hsl(30, 92%, 50%);
		--chart-5: hsl(25, 92%, 50%);
		--sidebar: hsl(40, 14%, 12%);
		--sidebar-foreground: hsl(40, 5%, 89.8%);
		--sidebar-primary: hsl(38, 96%, 50%);
		--sidebar-primary-foreground: hsl(0 0% 100%);
		--sidebar-accent: hsl(35, 92%, 44%);
		--sidebar-accent-foreground: hsl(40, 5%, 89.8%);
		--sidebar-border: hsl(40, 5%, 26.67%);
		--sidebar-ring: hsl(38, 96%, 50%);
	}
}
