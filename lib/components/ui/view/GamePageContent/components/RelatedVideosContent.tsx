"use client"
import React from "react"
import { useTranslations } from "next-intl"
import ReactPlayer from "react-player"
import { GameVideo } from "@/lib/types/api-types"

interface RelatedVideosContentProps {
	gameVideos: GameVideo[]
}

const RelatedVideosContent: React.FC<RelatedVideosContentProps> = ({
	gameVideos,
}) => {
	const t = useTranslations("Game")

	return (
		<div>
			{gameVideos.length > 0 ? (
				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-4 sm:mt-6">
					{gameVideos.map((video, index) => (
						<div
							key={`video-${index + 1}`}
							className="w-full"
						>
							<div className="aspect-video mb-2 rounded-lg overflow-hidden bg-muted">
								<ReactPlayer
									url={video.url}
									controls
									width="100%"
									height="100%"
									className="rounded-lg"
								/>
							</div>
						</div>
					))}
				</div>
			) : (
				<div className="text-center text-muted-foreground py-6 sm:py-8">
					{t("noGameVideos")}
				</div>
			)}
		</div>
	)
}

export default RelatedVideosContent
