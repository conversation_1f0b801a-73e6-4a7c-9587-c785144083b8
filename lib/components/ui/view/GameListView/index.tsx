"use client"

import React, { useState, useEffect, useMemo } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useTranslations } from "next-intl"
import { Search, Filter, Grid, List } from "lucide-react"
import { GameCard } from "@/lib/components/ui/view/GameCard"
import { GameCategorySidebar } from "./GameCategorySidebar"
import { GameListPagination } from "./GameListPagination"
import {
	ProjectGame,
	GameCategory,
	GameTag,
	GameLocaleContent,
} from "@/lib/types"
import { useSidebar } from "@/lib/hooks/use-sidebar"

interface GameListViewProps {
	games: ProjectGame[]
	categories: GameCategory[]
	tags: GameTag[]
	locale: string
	currentPage?: number
	totalPages?: number
	initialCategory?: string
	initialTag?: string
	gamesPerPage?: number
	useServerPagination?: boolean
}

const DEFAULT_GAMES_PER_PAGE = 20

export const GameListView: React.FC<GameListViewProps> = ({
	games,
	categories,
	tags,
	locale,
	currentPage = 1,
	totalPages = 1,
	initialCategory,
	initialTag,
	gamesPerPage = DEFAULT_GAMES_PER_PAGE,
	useServerPagination = false,
}) => {
	const t = useTranslations()
	const router = useRouter()
	const searchParams = useSearchParams()
	const { isGameBox } = useSidebar()

	// 状态管理
	const [selectedCategory, setSelectedCategory] = useState(
		initialCategory || "",
	)
	const [selectedTag, setSelectedTag] = useState(initialTag || "")
	const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
	const [showMobileFilters, setShowMobileFilters] = useState(false)

	// 转换游戏数据为本地化内容
	const gameLocaleContents = useMemo(() => {
		return games.map((game) => {
			// 查找对应语言的游戏内容
			const gameLocale = game.gameLocales?.find(
				(gameLocale) => gameLocale.locale === locale,
			)

			if (!gameLocale) {
				console.warn(`没有找到游戏详情内容:${game.id},locale:${locale}`)
				return {
					id: game.id,
					slug: game.slug,
					gameInfo: game.gameInfo,
					categories: game.categories,
					tags: game.tags,
					gameImages: game.gameImages,
					gameName: game.name,
					gameSlogan: "",
					gameDescription: "",
					updateTime: new Date().toISOString(),
					metadata: {
						title: game.name,
						description: "",
					},
					contents: [],
					isPrimary: game.isPrimary,
				} as GameLocaleContent & { isPrimary?: boolean }
			}

			return {
				...gameLocale.content,
				id: game.id,
				slug: game.slug,
				gameInfo: game.gameInfo,
				categories: game.categories,
				tags: game.tags,
				gameImages: game.gameImages,
				isPrimary: game.isPrimary,
			} as GameLocaleContent & { isPrimary?: boolean }
		})
	}, [games, locale])

	// 计算分类和标签的游戏数量，并过滤掉没有游戏的分类和标签
	const categoriesWithCount = useMemo(() => {
		return categories
			.map((category) => {
				const count = gameLocaleContents.filter((game) =>
					game.categories?.includes(category.code),
				).length
				return {
					...category,
					count,
				}
			})
			.filter((category) => category.count > 0) // 只显示有游戏的分类
	}, [categories, gameLocaleContents])

	const tagsWithCount = useMemo(() => {
		return tags
			.map((tag) => {
				const count = gameLocaleContents.filter((game) =>
					game.tags?.includes(tag.slug),
				).length
				return {
					...tag,
					count,
				}
			})
			.filter((tag) => tag.count > 0) // 只显示有游戏的标签
	}, [tags, gameLocaleContents])

	// 在服务端分页模式下，games已经是过滤和分页后的数据
	// 在客户端模式下，需要进行过滤和分页
	const displayGames = useServerPagination
		? gameLocaleContents
		: gameLocaleContents

	// 更新URL参数
	const updateURL = (params: Record<string, string | undefined>) => {
		if (useServerPagination) {
			// 服务端分页模式
			const page = params.page ? Number.parseInt(params.page) : 1
			let baseURL = "/games"

			if (page > 1) {
				baseURL = `/games/page/${page}`
			}

			const newSearchParams = new URLSearchParams()
			Object.entries(params).forEach(([key, value]) => {
				if (key !== "page" && value) {
					newSearchParams.set(key, value)
				}
			})

			const queryString = newSearchParams.toString()
			const newURL = queryString ? `${baseURL}?${queryString}` : baseURL
			router.push(newURL, { scroll: false })
		} else {
			// 客户端分页模式
			const newSearchParams = new URLSearchParams(searchParams.toString())

			Object.entries(params).forEach(([key, value]) => {
				if (value) {
					newSearchParams.set(key, value)
				} else {
					newSearchParams.delete(key)
				}
			})

			const newURL = `/games?${newSearchParams.toString()}`
			router.push(newURL, { scroll: false })
		}
	}

	// 处理分类选择
	const handleCategoryChange = (categoryCode: string) => {
		setSelectedCategory(categoryCode)
		updateURL({
			category: categoryCode || undefined,
			tag: selectedTag || undefined,
		})
	}

	// 处理标签选择
	const handleTagChange = (tagSlug: string) => {
		setSelectedTag(tagSlug)
		updateURL({
			tag: tagSlug || undefined,
			category: selectedCategory || undefined,
		})
	}

	// 处理分页
	const handlePageChange = (page: number) => {
		if (useServerPagination) {
			// 服务端分页直接跳转
			updateURL({
				page: page.toString(),
				category: selectedCategory || undefined,
				tag: selectedTag || undefined,
			})
		}
	}

	return (
		<div className={isGameBox ? "w-full" : "flex flex-col lg:flex-row gap-6"}>
			{/* 左侧分类导航 - 在GameBox模板中隐藏 */}
			{!isGameBox && (
				<aside className="lg:w-64 flex-shrink-0">
					<GameCategorySidebar
						categories={categoriesWithCount}
						tags={tagsWithCount}
						selectedCategory={selectedCategory}
						selectedTag={selectedTag}
						onCategoryChange={handleCategoryChange}
						onTagChange={handleTagChange}
						className={`lg:block ${showMobileFilters ? "block" : "hidden"}`}
					/>
				</aside>
			)}

			{/* 游戏列表 */}
			<main className="flex-1">
				{/* 搜索框 */}
				<div className="mb-6">
					<form
						onSubmit={(e) => {
							e.preventDefault()
							const formData = new FormData(e.currentTarget)
							const query = formData.get("q") as string
							if (query.trim()) {
								router.push(`/search?q=${encodeURIComponent(query.trim())}`)
							}
						}}
						className="relative"
					>
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
						<input
							name="q"
							type="text"
							placeholder={t("GameList.searchPlaceholder")}
							className="w-full pl-10 pr-20 py-3 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
						/>
						<button
							type="submit"
							className="absolute right-2 top-1/2 transform -translate-y-1/2 px-4 py-1.5 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
						>
							{t("GameList.search")}
						</button>
					</form>
				</div>

				{/* 工具栏 */}
				<div className="mb-6">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-4">
							{/* 移动端筛选按钮 - 在GameBox模板中隐藏 */}
							{!isGameBox && (
								<button
									onClick={() => setShowMobileFilters(!showMobileFilters)}
									className="lg:hidden flex items-center gap-2 px-3 py-2 border border-border rounded-lg bg-background text-foreground hover:bg-muted"
								>
									<Filter className="h-4 w-4" />
									{t("GameList.filters")}
								</button>
							)}

							{/* 结果统计 */}
							<span className="text-sm text-muted-foreground">
								{t("GameList.resultsCount", {
									count: gameLocaleContents.length,
									total: games.length,
								})}
							</span>
						</div>

						{/* 视图模式切换 */}
						<div className="flex items-center gap-2">
							<button
								onClick={() => setViewMode("grid")}
								className={`p-2 rounded-lg ${
									viewMode === "grid"
										? "bg-primary text-primary-foreground"
										: "bg-background text-foreground hover:bg-muted"
								}`}
							>
								<Grid className="h-4 w-4" />
							</button>
							<button
								onClick={() => setViewMode("list")}
								className={`p-2 rounded-lg ${
									viewMode === "list"
										? "bg-primary text-primary-foreground"
										: "bg-background text-foreground hover:bg-muted"
								}`}
							>
								<List className="h-4 w-4" />
							</button>
						</div>
					</div>
				</div>

				{/* 游戏网格/列表 */}
				{gameLocaleContents.length > 0 ? (
					<div
						className={
							viewMode === "grid"
								? "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 mb-8"
								: "space-y-4 mb-8"
						}
					>
						{gameLocaleContents.map((game) => (
							<GameCard key={game.id} {...game} />
						))}
					</div>
				) : (
					<div className="text-center py-12 bg-muted/50 rounded-lg mb-8">
						<p className="text-muted-foreground text-lg">
							{t("GameList.noGamesFound")}
						</p>
						{(selectedCategory || selectedTag) && (
							<button
								type="button"
								onClick={() => {
									setSelectedCategory("")
									setSelectedTag("")
									updateURL({})
								}}
								className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90"
							>
								{t("GameList.clearFilters")}
							</button>
						)}
					</div>
				)}

				{/* 分页 */}
				{totalPages > 1 && (
					<GameListPagination
						currentPage={currentPage}
						totalPages={totalPages}
						onPageChange={handlePageChange}
					/>
				)}
			</main>
		</div>
	)
}
