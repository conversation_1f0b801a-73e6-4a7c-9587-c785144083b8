import { Metadata } from "next"
import { setRequestLocale, getTranslations } from "next-intl/server"
import { locales as apiLocales } from "@/lib/i18n/locales"
import { Breadcrumb } from "@/lib/components/ui/view/Breadcrumb"
import { UnifiedSearchView } from "@/lib/components/ui/view/UnifiedSearchView"
import { SidebarProvider } from "@/lib/components/ui/sidebar"
import {
	getGames,
	getGameCategoriesByLocale,
	getGameTagsByLocale,
	getSiteSettings
} from "@/lib/services/api-client"
import { ProjectGame, GameCategory, GameTag, BreadcrumbItem, GameTemplateType } from "@/lib/types"

export const dynamic = 'force-static'

const RESULTS_PER_PAGE = 20

type Props = {
	params: Promise<{ locale: string }>
	searchParams: Promise<{
		q?: string
		page?: string
		type?: 'all' | 'games' | 'articles'
	}>
}

export async function generateStaticParams() {
	return apiLocales.map((locale) => ({
		locale,
	}))
}

export async function generateMetadata({ params, searchParams }: Props): Promise<Metadata> {
	const { locale } = await params
	const { q } = await searchParams
	setRequestLocale(locale)
	const t = await getTranslations()
	const siteSettings = await getSiteSettings()

	const title = q
		? `${t("Search.searchResults", { query: q })} - ${siteSettings.siteName}`
		: `${t("Search.title")} - ${siteSettings.siteName}`

	return {
		title,
		description: t("Search.description"),
		openGraph: {
			title,
			description: t("Search.description"),
		},
	}
}

export default async function SearchPage({ params, searchParams }: Props) {
	const { locale } = await params
	const { q, page, type = 'all' } = await searchParams

	setRequestLocale(locale)
	const t = await getTranslations()

	const currentPage = page ? parseInt(page) : 1

	// 获取网站设置以检测模板类型
	const siteSettings = await getSiteSettings()
	const isGameBox = siteSettings.templateType === GameTemplateType.GameBox

	// 获取数据
	const [games, categories, tags] = await Promise.all([
		getGames(),
		getGameCategoriesByLocale(locale),
		getGameTagsByLocale(locale)
	])

	// 搜索游戏
	let searchedGames: ProjectGame[] = []
	if (q && q.trim() && (type === 'all' || type === 'games')) {
		const searchTerm = q.toLowerCase()
		searchedGames = games.filter(game => {
			// 查找对应语言的游戏内容
			const gameLocale = game.gameLocales?.find(
				(gameLocale) => gameLocale.locale === locale,
			)

			if (!gameLocale) return false

			const content = gameLocale.content
			return (
				content.gameName?.toLowerCase().includes(searchTerm) ||
				content.gameDescription?.toLowerCase().includes(searchTerm)
			)
		})
	}

	// TODO: 搜索文章 - 后续实现
	let searchedArticles: any[] = []
	if (q && q.trim() && (type === 'all' || type === 'articles')) {
		// 这里后续添加文章搜索逻辑
		searchedArticles = []
	}

	// 计算总结果数
	const totalGames = searchedGames.length
	const totalArticles = searchedArticles.length
	const totalResults = totalGames + totalArticles

	// 分页处理 - 根据类型过滤
	let paginatedResults: any[] = []
	let totalPages = 1

	if (type === 'games') {
		totalPages = Math.ceil(totalGames / RESULTS_PER_PAGE)
		const startIndex = (currentPage - 1) * RESULTS_PER_PAGE
		paginatedResults = searchedGames.slice(startIndex, startIndex + RESULTS_PER_PAGE)
	} else if (type === 'articles') {
		totalPages = Math.ceil(totalArticles / RESULTS_PER_PAGE)
		const startIndex = (currentPage - 1) * RESULTS_PER_PAGE
		paginatedResults = searchedArticles.slice(startIndex, startIndex + RESULTS_PER_PAGE)
	} else {
		// 混合结果 - 游戏和文章混合显示
		const allResults = [
			...searchedGames.map(game => ({ type: 'game', data: game })),
			...searchedArticles.map(article => ({ type: 'article', data: article }))
		]
		totalPages = Math.ceil(allResults.length / RESULTS_PER_PAGE)
		const startIndex = (currentPage - 1) * RESULTS_PER_PAGE
		paginatedResults = allResults.slice(startIndex, startIndex + RESULTS_PER_PAGE)
	}

	// 面包屑导航
	const breadcrumbItems: BreadcrumbItem[] = [
		{ label: t("Common.Home"), href: "/" },
		{ label: t("Search.title"), href: "/search", isActive: true },
	]

	const content = (
		<>
			<Breadcrumb items={breadcrumbItems} />
			<main className="container mx-auto px-4 py-6">
				<div className="mb-6">
					<h1 className="text-3xl font-bold text-foreground mb-2">
						{q ? t("Search.searchResults", { query: q }) : t("Search.title")}
					</h1>
					<p className="text-muted-foreground">
						{q
							? t("Search.resultsCount", { count: totalResults, query: q })
							: t("Search.subtitle")
						}
					</p>
				</div>

				<UnifiedSearchView
					query={q}
					results={paginatedResults}
					totalGames={totalGames}
					totalArticles={totalArticles}
					totalResults={totalResults}
					currentPage={currentPage}
					totalPages={totalPages}
					currentType={type}
					locale={locale}
					resultsPerPage={RESULTS_PER_PAGE}
				/>
			</main>
		</>
	)

	// 只在GameBox模板下使用SidebarProvider
	return isGameBox ? (
		<SidebarProvider>{content}</SidebarProvider>
	) : (
		content
	)
}
