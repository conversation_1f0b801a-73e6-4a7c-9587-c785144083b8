import { Metadata } from "next"
import { setRequestLocale, getTranslations } from "next-intl/server"
import { locales as apiLocales } from "@/lib/i18n/locales"
import { Breadcrumb } from "@/lib/components/ui/view/Breadcrumb"
import { GameListView } from "@/lib/components/ui/view/GameListView"

import {
	getGames,
	getGameCategoriesByLocale,
	getGameTagsByLocale,
	getSiteSettings
} from "@/lib/services/api-client"
import { ProjectGame, GameCategory, GameTag, BreadcrumbItem } from "@/lib/types"

export const dynamic = 'force-static'

const GAMES_PER_PAGE = 50

type Props = {
	params: Promise<{ locale: string }>
	searchParams: Promise<{
		category?: string
		tag?: string
	}>
}

export async function generateStaticParams() {
	return apiLocales.map((locale) => ({
		locale,
	}))
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { locale } = await params
	setRequestLocale(locale)
	const t = await getTranslations()
	const siteSettings = await getSiteSettings()

	return {
		title: `${t("Common.GameList")} - ${siteSettings.siteName}`,
		description: t("GameList.description"),
		openGraph: {
			title: `${t("Common.GameList")} - ${siteSettings.siteName}`,
			description: t("GameList.description"),
		},
	}
}

export default async function GamesPage({ params, searchParams }: Props) {
	const { locale } = await params
	const { category, tag } = await searchParams

	setRequestLocale(locale)
	const t = await getTranslations()

	// 获取游戏数据
	const [games, categories, tags] = await Promise.all([
		getGames(),
		getGameCategoriesByLocale(locale),
		getGameTagsByLocale(locale)
	])

	// 服务端过滤游戏
	let filteredGames = games

	// 按分类过滤
	if (category) {
		filteredGames = filteredGames.filter(game =>
			game.categories?.includes(category)
		)
	}

	// 按标签过滤
	if (tag) {
		filteredGames = filteredGames.filter(game =>
			game.tags?.includes(tag)
		)
	}

	// 分页处理
	const totalPages = Math.ceil(filteredGames.length / GAMES_PER_PAGE)
	const currentPage = 1 // 第1页
	const startIndex = (currentPage - 1) * GAMES_PER_PAGE
	const paginatedGames = filteredGames.slice(startIndex, startIndex + GAMES_PER_PAGE)

	// 面包屑导航
	const breadcrumbItems: BreadcrumbItem[] = [
		{ label: t("Common.Home"), href: "/" },
		{ label: t("Common.GameList"), href: "/games", isActive: true },
	]

	return (
		<>
			<Breadcrumb items={breadcrumbItems} />
			<main className="container mx-auto px-4 py-6">
				<div className="mb-6">
					<h1 className="text-3xl font-bold text-foreground mb-2">
						{t("Common.GameList")}
					</h1>
					<p className="text-muted-foreground">
						{t("GameList.subtitle", { count: filteredGames.length })}
					</p>
				</div>

				<GameListView
					games={paginatedGames}
					categories={categories}
					tags={tags}
					locale={locale}
					currentPage={currentPage}
					totalPages={totalPages}
					initialCategory={category}
					initialTag={tag}
					gamesPerPage={GAMES_PER_PAGE}
					useServerPagination={true}
				/>
			</main>
		</>
	)
}
