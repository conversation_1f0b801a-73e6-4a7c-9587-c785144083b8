import React from "react"
import { Metada<PERSON> } from "next"
import { Breadcrumb } from "@/lib/components/ui/view/Breadcrumb"
import { defaultLocale } from "@/lib/i18n/locales"
import { setRequestLocale } from "next-intl/server"
import { getSiteSettings } from "@/lib/services/api-client"
import TermsContent from "./view"
export const dynamic = 'force-static';
type Props = {
	params: Promise<{ locale: string }>
}

export async function generateStaticParams() {
	try {
		return [defaultLocale].map((locale) => ({ locale }))
	} catch (error) {
		console.error("Failed to fetch locales for generateStaticParams:", error)
		return []
	}
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { locale } = await params
	setRequestLocale(locale)

	return {
		title: "Terms of Service",
		description:
			"Our terms of service outline the rules and guidelines for using our website and services.",
		robots: {
			index: false,
			follow: false,
		},
	}
}

export default async function TermsPage({ params }: Props) {
	const { locale } = await params
	setRequestLocale(locale)

	// Get site configuration from API
	const siteConfig = await getSiteSettings()

	// Breadcrumb navigation items
	const breadcrumbItems = [
		{ label: "Home", href: "/" },
		{ label: "Terms of Service", href: "/terms", isActive: true },
	]

	return (
		<>
			<Breadcrumb items={breadcrumbItems} />
			<main className="container mx-auto px-4 py-8">
				<TermsContent siteConfig={siteConfig} />
			</main>
		</>
	)
}
