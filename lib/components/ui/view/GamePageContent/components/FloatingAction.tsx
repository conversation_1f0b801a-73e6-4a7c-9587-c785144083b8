'use client'
import ClientButton from '@/lib/components/ui/view/ClientButton';
import { Icon } from '@/lib/components/common';
import { useTranslations } from 'next-intl';
export default function FloatingAction() {
  const t = useTranslations();
    return (
        <div className="fixed bottom-6 right-6 z-50 flex flex-col space-y-3">
        {/* Back to Top Button */}
        <ClientButton
          id="back-to-top"
          title={t('Common.backToTop') || 'Back to Top'}
          className="bg-primary dark:bg-slate-700 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 dark:hover:bg-slate-600 transition-colors backdrop-blur-xl"
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          icon={<Icon name="ChevronRight" className="h-6 w-6 -rotate-90" size={24} />}
        />

        {/* Quick Nav: To Game */}
        <ClientButton
          id="to-game"
          title={t('Common.backToGame') || 'Back to Game'}
          className="bg-primary dark:bg-slate-700 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 dark:hover:bg-slate-600 transition-colors backdrop-blur-xl"
          onClick={() => {
            // Attempt to find the game iframe container
            const gameElement = document.querySelector('.aspect-w-16'); // Simple selector, might need refinement
            if (gameElement) gameElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }}
          icon={<Icon name="Gamepad2" className="h-6 w-6" size={24} />}
        />

        {/* Quick Nav: To Info (Scroll to Tabs or specific section) */}
        <ClientButton
          id="to-info"
          title={t('Common.gameInfo') || 'Game Info'}
          className="bg-primary dark:bg-slate-700 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 dark:hover:bg-slate-600 transition-colors backdrop-blur-xl"
          onClick={() => {
            const infoElement = document.getElementById('game-info') || document.getElementById('content-navigation-tabs'); // Fallback to tabs
            if (infoElement) infoElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }}
          icon={<Icon name="Info" className="h-6 w-6" size={24} />}
        />

        {/* Quick Nav: To Comments */}
        <ClientButton
          id="to-comments"
          title={t('Common.playerComments') || 'Player Comments'}
          className="bg-primary dark:bg-slate-700 text-white p-3 rounded-full shadow-lg hover:bg-indigo-700 dark:hover:bg-slate-600 transition-colors backdrop-blur-xl"
          onClick={() => {
            const commentsElement = document.getElementById('game-comments');
            if (commentsElement) commentsElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }}
          icon={<Icon name="MessageCircle" className="h-6 w-6" size={24} />}
        />
      </div>
    )
}
