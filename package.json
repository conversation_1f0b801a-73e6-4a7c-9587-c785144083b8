{"name": "nex<PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbo", "fetch-site-settings": "bun run scripts/fetch-site-settings.ts", "build": "next build && node scripts/post-build.js", "start": "next start", "lint": "biome lint .", "lint:fix": "biome lint . --apply", "format": "biome format . --write", "format:check": "biome format .", "check": "biome check .", "fix": "biome check . --apply", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:push": "prisma db push", "db:dashboard": "prisma studio", "db:format": "prisma format", "registry:build": "shadcn registry:build", "prepare": "husky"}, "dependencies": {"@aws-sdk/client-s3": "^3.797.0", "@aws-sdk/s3-request-presigner": "^3.797.0", "@hookform/resolvers": "^5.0.1", "@next/mdx": "^15.3.2", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-context-menu": "^2.2.12", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.12", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@react-aria/ssr": "3.9.8", "@react-aria/visually-hidden": "3.8.22", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-table": "^8.21.3", "bytes": "^3.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.6.0", "formidable": "3.5.4", "framer-motion": "^12.11.3", "input-otp": "^1.4.2", "itty-router": "^5.0.18", "lucide-react": "^0.503.0", "next": "^15.3.1", "next-intl": "^4.1.0", "next-mdx-remote": "^5.0.0", "next-themes": "^0.4.6", "qrcode.react": "^4.2.0", "radash": "^12.1.0", "react": "19.1.0", "react-day-picker": "9.6.7", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.1", "react-markdown": "^10.1.0", "react-player": "^2.16.0", "react-resizable-panels": "^2.1.9", "react-use": "^17.6.0", "recharts": "^2.15.3", "rehype-raw": "^7.0.0", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "remark-mdx-frontmatter": "^5.1.0", "shadcn": "^2.6.0-canary.2", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.2.0", "vaul": "^1.1.2", "zod": "^3.24.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/bytes": "^3.1.5", "@types/node": "22.15.3", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "autoprefixer": "10.4.21", "husky": "^9.1.7", "postcss": "^8.5.3", "prisma": "^6.6.0", "prisma-query-log": "^3.2.1", "tailwind-scrollbar": "^4.0.2", "tailwind-variants": "1.0.0", "tailwindcss": "^4.1.4", "tailwindcss-animate": "^1.0.7", "turbo": "^2.5.2", "tw-animate-css": "^1.2.8", "typescript": "5.8.3"}}