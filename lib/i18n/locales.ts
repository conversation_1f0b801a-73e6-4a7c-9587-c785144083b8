import type { LocalePrefix, Pathnames } from "next-intl/routing"
import type { NextRequest } from "next/server"
import { getLocaleNames} from "@/lib/services/api-client"
import { Language } from "../types";

import cachedSiteSettings from "@/lib/config/siteSettings"

export const locales = cachedSiteSettings.languanges
export const defaultLocale = cachedSiteSettings.defaultLocale


// as-needed means that the locale prefix is only added to the pathname if it is not already present.
export const localePrefix: LocalePrefix =
	"as-needed" as LocalePrefix


export type I18nLocales = { key: string; name: string }[]

export const getCurrentLocaleName = async (locale: string) => {
	const data = await getLocaleNames()
	return data[locale]
}

// 异步获取语言名称
export async function getLocaleName(locale: string): Promise<string> {
  const names = await getLocaleNames()
  const language = names[locale] as Language;
  return language.localName?? "";
}

/**
 * 获取不带语言前缀的路径名
 * @param path 路径，可以是字符串、URL对象或NextRequest对象
 * @returns 不带语言前缀的路径名
 */
export  function getPathnameWithoutLocale(
	path: string | URL | NextRequest,
): string {
	let pathname: string
	if (typeof path === "string") {
		pathname = path
	} else if (path instanceof URL) {
		pathname = path.pathname
	} else {
		pathname = path.nextUrl.pathname
	}

	// 检查路径是否以语言代码开头
	for (const locale of locales) {
		if (pathname.startsWith(`/${locale}/`)) {
			return pathname.slice(locale.length + 1)
		}
	}

	return pathname
}

export  function getPathnameWithLocale(pathname: string, locale: string) {
	if (locale === defaultLocale) {
		return pathname
	}
	if (pathname.startsWith("/")) {
		return `/${locale}${pathname}`
	}

	return pathname
}

// 生成多语言的alternates
export  function alternatesLanguage(subPath: string) {
	const path = process.env.NEXT_PUBLIC_DOMAIN || ''
	const languages: Record<string, string> = {}
	// 检查路径是否以语言代码开头
	locales.forEach((lang) => {
		languages[lang] =
			lang === defaultLocale ? `${path}${subPath}` : `${path}/${lang}${subPath}`
	})
	return languages
}

export  function alternatesCanonical(
	locale: string,
	subPath: string,
	page?: string,
) {
	const path = process.env.NEXT_PUBLIC_DOMAIN || ''
	const withPages = page ? `/${page}` : ""
	return `${path}${
		defaultLocale === locale ? "" : `/${locale}`
	}${subPath}${withPages}`
}

/**
 * 生成 hreflang 标签数据
 * @param subPath 子路径
 * @returns hreflang 标签数据
 */
export  function generateHreflangData(subPath: string) {
  const languages =  alternatesLanguage(subPath);
  // 添加 x-default 链接
  const xDefault = languages[defaultLocale];
  return {
    languages,
    'x-default': xDefault
  };
}

