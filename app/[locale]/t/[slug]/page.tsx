import MarkdownRenderer from "@/lib/components/common/MarkdownRenderer"
import { Breadcrumb } from "@/lib/components/ui/view/Breadcrumb"
import { alternatesCanonical, generateHreflangData } from "@/lib/i18n"
import {
	getArticleByLocale,
	getArticleBySlug,
	getSiteSettings,
} from "@/lib/services/api-client"
import {
	ArticlePost as BaseArticlePost,
	BreadcrumbItem,
} from "@/lib/types/api-types"

// 扩展ArticlePost类型，添加href属性
type ArticlePost = BaseArticlePost & { href?: string }

import {
	combineSchemas,
	generateBlogPostSchema,
	generateBreadcrumbSchema,
} from "@/lib/utils/schema"
import { Link } from "@lib/i18n"
import {
	Calendar,
	ChevronLeft,
	ChevronRight,
	Clock,
	Facebook,
	Linkedin,
	Mail,
	Twitter,
} from "lucide-react"
import { Metadata } from "next"
import { getTranslations, setRequestLocale } from "next-intl/server"
import Image from "next/image"
import { notFound } from "next/navigation"

import { locales as apiLocales, defaultLocale } from "@/lib/i18n/locales"
import ClientImage from "@/lib/components/ui/view/ClientImage"
export const dynamic = "force-static"
type Props = {
	params: Promise<{ locale: string; slug: string }>
}

export async function generateStaticParams() {
	try {
		// Generate parameters for each locale and blog post
		const params = []
		// Get all blog posts
		const posts = await getArticleByLocale(defaultLocale)
		if (!posts || posts.length === 0) {
			console.warn(
				"generateStaticParams: No blog posts found, returning empty params.",
			)
			for (const locale of apiLocales) {
				params.push({
					locale,
					slug: "index",
				})
			}
			return params
		}

		for (const locale of apiLocales) {
			for (const post of posts) {
				// Ensure post and post.slug are valid
				if (post?.slug) {
					params.push({
						locale,
						slug: post.slug,
					})
				} else {
					console.warn(
						`generateStaticParams: Skipping post with invalid slug for locale ${locale}. Post:`,
						post,
					)
				}
			}
		}

		if (params.length === 0) {
			console.warn(
				"generateStaticParams: No valid params generated, returning empty array. This might mean no pages will be pre-rendered for this route.",
			)
		}
		return params
	} catch (error) {
		console.error("Failed to fetch data for generateStaticParams:", error)
		return [] // Ensure an empty array is returned on error
	}
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { locale, slug } = await params
	setRequestLocale(locale)
	const ct = await getTranslations()
	const t = await getTranslations("BlogPage")

	// 获取博客文章数据
	const post = await getArticleBySlug(locale, slug)
	if (!post) {
		return notFound()
	}

	// 构建页面路径（数据中已经有/t/）
	const path = `${slug}`
	const canonicalUrl = alternatesCanonical(locale, path)
	const hreflangData = generateHreflangData(path)

	// 构建面包屑导航项
	const breadcrumbItems: BreadcrumbItem[] = [
		{ label: ct("Common.Home"), href: "/" },
		{ label: ct("Common.Blog"), href: "/blogs" },
		{ label: post.title, isActive: true, href: path },
	]
	const siteSettings = await getSiteSettings()
	// 生成结构化数据
	const blogSchema = generateBlogPostSchema(post, locale, siteSettings)
	const breadcrumbSchema = generateBreadcrumbSchema(
		breadcrumbItems,
		canonicalUrl,
	)
	const structuredData = combineSchemas(blogSchema, breadcrumbSchema)
	const metadata = post.metadata
	// 返回增强的元数据
	return {
		title: `${metadata.title}| ${siteSettings.siteName}`,
		description: metadata.description,
		openGraph: {
			title: metadata.title,
			description: metadata.description,
			images: [{ url: post.titleImageUrl }],
			type: "article",
			publishedTime: post.updateTime,
			authors: [post.author ? post.author : ""],
			tags: post.category?.name,
			url: canonicalUrl,
		},
		twitter: {
			card: "summary_large_image",
			title: post.title,
			description: metadata.description,
			images: [post.titleImageUrl],
		},
		alternates: {
			languages: hreflangData.languages,
			canonical: canonicalUrl,
		},
		other: {
			"script:ld+json": JSON.stringify(structuredData),
		},
	}
}

export default async function PostsPage({ params }: Props) {
	const { locale, slug } = await params
	setRequestLocale(locale)
	const ct = await getTranslations()
	const t = await getTranslations("BlogPage")

	// Get blog post data from API
	const post = await getArticleBySlug(locale, slug)
	if (!post) {
		return notFound()
	}
	// Get related posts, or fetch latest posts if none are provided
	// let relatedPosts: ArticlePost[] = post.relatedPosts || []
	const relatedPosts: ArticlePost[] = []
	// 确保所有relatedPosts都有href属性
	// if (relatedPosts.length === 0) {
	// 	const allPosts = await getArticleByLocale(locale)
	// 	relatedPosts = allPosts
	// 		.filter((p) => p.slug !== slug)
	// 		.slice(0, 3)
	// 		.map((p) => ({
	// 			...p,
	// 			href: `${p.slug}`,
	// 		}))
	// }

	// 构建面包屑导航项
	const breadcrumbItems: BreadcrumbItem[] = [
		{ label: ct("Common.Home"), href: "/" },
		{ label: ct("Common.Blog"), href: "/blogs" },
		{ label: post.title, isActive: true, href: slug },
	]

	return (
		<>
			<Breadcrumb items={breadcrumbItems} />

			<main className="container mx-auto bg-white px-4 py-8">
				<article className="max-w-4xl mx-auto">
					{/* Article header */}
					<header className="mb-8">
						<div className="flex items-center text-sm text-gray-500 mb-4">
							<span className="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full">
								{post.category?.name}
							</span>
							<span className="mx-2">•</span>
							<div className="flex items-center">
								<Calendar className="h-4 w-4 mr-1" />
								<span>{post.updateTime}</span>
							</div>
							<span className="mx-2">•</span>
							<div className="flex items-center">
								<Clock className="h-4 w-4 mr-1" />
								<span>{post.readTime}</span>
							</div>
						</div>

						<h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
							{post.title}
						</h1>

						<p className="text-xl text-gray-600 mb-6">
							{post.metadata.description}
						</p>

						<div className="flex items-center">
							<ClientImage
								src={post.authorImageUrl || ""}
								alt={post.author || ""}
								className="rounded-full mr-3"
							/>
							<div>
								<div className="font-medium text-gray-900">{post.author}</div>
								<div className="text-sm text-gray-500">{t("author")}</div>
							</div>
						</div>
					</header>

					{/* Featured image */}
					<div className="relative h-64 md:h-96 mb-8 rounded-xl overflow-hidden">
						<ClientImage
							src={post.titleImageUrl || ""}
							alt={post.title}
							className="object-cover"
						/>
					</div>

					{/* Article content */}
					<div className="prose prose-lg max-w-none mb-12">
						<MarkdownRenderer content={post.mdxContent} />
					</div>

					{/* Share buttons */}
					<div className="border-t border-b border-gray-200 py-6 mb-8">
						<div className="flex items-center">
							<span className="text-gray-700 font-medium mr-4">
								{t("shareThisArticle")}
							</span>
							<div className="flex space-x-2">
								<button
									type="button"
									className="p-2 rounded-full bg-gray-100 hover:bg-blue-100 transition-colors"
									aria-label="Share on Facebook"
									title="Share on Facebook"
								>
									<Facebook className="h-5 w-5 text-gray-600 hover:text-blue-600" />
								</button>
								<button
									type="button"
									className="p-2 rounded-full bg-gray-100 hover:bg-blue-100 transition-colors"
									aria-label="Share on Twitter"
									title="Share on Twitter"
								>
									<Twitter className="h-5 w-5 text-gray-600 hover:text-blue-400" />
								</button>
								<button
									type="button"
									className="p-2 rounded-full bg-gray-100 hover:bg-blue-100 transition-colors"
									aria-label="Share on LinkedIn"
									title="Share on LinkedIn"
								>
									<Linkedin className="h-5 w-5 text-gray-600 hover:text-blue-700" />
								</button>
								<button
									type="button"
									className="p-2 rounded-full bg-gray-100 hover:bg-red-100 transition-colors"
									aria-label="Share via Email"
									title="Share via Email"
								>
									<Mail className="h-5 w-5 text-gray-600 hover:text-red-600" />
								</button>
							</div>
						</div>
					</div>

					{/* Navigation between articles */}
					<div className="flex flex-col sm:flex-row justify-between mb-12">
						<Link
							href="/blogs"
							className="flex items-center text-indigo-600 hover:text-indigo-800 mb-4 sm:mb-0"
						>
							<ChevronLeft className="h-4 w-4 mr-1" />
							{t("backToBlog")}
						</Link>
						{relatedPosts.length > 1 && (
							<div className="flex space-x-4">
								<Link
									href={relatedPosts[0]?.href || "/blogs"}
									className="flex items-center text-indigo-600 hover:text-indigo-800"
								>
									<ChevronLeft className="h-4 w-4 mr-1" />
									{t("previousArticle")}
								</Link>

								<Link
									href={relatedPosts[1]?.href || "/blogs"}
									className="flex items-center text-indigo-600 hover:text-indigo-800"
								>
									{t("nextArticle")}
									<ChevronRight className="h-4 w-4 ml-1" />
								</Link>
							</div>
						)}
					</div>
				</article>

				{/* Related articles */}
				{relatedPosts.length > 0 && (
					<section className="max-w-4xl mx-auto">
						<h2 className="text-2xl font-bold text-gray-900 mb-6">
							{t("relatedArticles")}
						</h2>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
							{relatedPosts.map((post: any) => (
								<Link key={post.id} href={post.href} className="group">
									<div className="bg-white rounded-lg overflow-hidden shadow-md group-hover:shadow-lg transition-shadow">
										<div className="relative h-40">
											<ClientImage
												src={post.image}
												alt={post.title}
												className="object-cover transition-transform group-hover:scale-105 duration-300"
											/>
											<div className="absolute top-0 left-0 bg-indigo-600 text-white text-xs font-bold px-3 py-1 m-3 rounded">
												{post.category}
											</div>
										</div>
										<div className="p-4">
											<h3 className="font-bold text-gray-900 mb-2 group-hover:text-indigo-600 transition-colors">
												{post.title}
											</h3>
											<p className="text-gray-600 text-sm mb-3 line-clamp-2">
												{post.excerpt}
											</p>
											<div className="text-xs text-gray-500">{post.date}</div>
										</div>
									</div>
								</Link>
							))}
						</div>
					</section>
				)}
			</main>
		</>
	)
}
