import React from "react"
import { <PERSON>ada<PERSON> } from "next"
import { Breadcrumb } from "@/lib/components/ui/view/Breadcrumb"
import { GameContentContainer } from "@/lib/components/ui/view/GamePageContent"
import { GameboxHomePage } from "@/lib/components/ui/view/GameboxHomePage"
import {
	getSiteSettings,
	getProjectGame,
	getHomePageGame,
	getGameLocaleContent,
} from "@/lib/services/api-client"
import { locales } from "@/lib/i18n/locales"
import { setRequestLocale } from "next-intl/server"
import { enhanceGameMetadata, generateGameStructuredData } from "@/lib/utils/metadata"
import {
	GameTemplateType,
	GameLocaleContent,
	BreadcrumbItem,
} from "@/lib/types"
import { getTranslations } from "next-intl/server"
export const dynamic = 'force-static'
type Props = {
	params: Promise<{ locale: string }>
}

export async function generateStaticParams() {
	try {
		// 从API获取支持的语言列表
		const apiLocales = locales
		return apiLocales.map((locale) => ({ locale }))
	} catch (error) {
		console.error("Failed to fetch locales for generateStaticParams:", error)
		return []
	}
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { locale } = await params
	if (!locale) {
		return { title: "Locale Not Found" }
	}
	setRequestLocale(locale)
	const homePageGame = await getHomePageGame()
	// 使用增强的元数据函数(首页的路径为空，不能是/)
	const metadata = await enhanceGameMetadata("", homePageGame, locale)
	return {
		...metadata,
	}
}

export default async function HomePage({ params }: Props) {
	const { locale } = await params
	setRequestLocale(locale)
	const siteConfig = await getSiteSettings()
	if (siteConfig.templateType === GameTemplateType.GameBox) {
		return <GameboxHomePage locale={locale} />
	}
	const t = await getTranslations()
	const homePageGame = await getHomePageGame()
	const gameInfo = await getProjectGame(homePageGame.id)
	const gameLocaleContent: GameLocaleContent = await getGameLocaleContent(
		locale,
		gameInfo,
	)
	const breadcrumbItems: BreadcrumbItem[] = [
		{ label: t("Common.Home"), href: "/" },
		...(gameLocaleContent.breadcrumbItems || []),
	]

	// 获取结构化数据
	const structuredData = await generateGameStructuredData("", gameInfo, locale)
	return (
		<>
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
			/>
			<Breadcrumb items={breadcrumbItems} />
			<main className="container mx-auto px-4 py-6">
				<GameContentContainer gameId={gameInfo.id} locale={locale} />
			</main>
		</>
	)
}
