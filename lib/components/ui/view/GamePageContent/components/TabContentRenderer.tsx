import React from "react"
import { GameDetailContent, GameDetailContentType, GameVideo } from "@/lib/types/api-types"
import RelatedVideosContent from "./RelatedVideosContent"
import MarkdownContent from "./MarkdownContent"

interface TabContentRendererProps {
	tab: GameDetailContent
	gameVideos: GameVideo[]
	className?: string
}

/**
 * Tab 内容渲染器组件
 * 根据 tab.type 类型渲染不同的内容组件
 */
const TabContentRenderer: React.FC<TabContentRendererProps> = ({ 
	tab, 
	gameVideos, 
	className = "" 
}) => {
	// 跳过没有内容的标签
	if (!tab.text && !tab.jsonContent) return null

	return (
		<div
			key={tab.tabId}
			id={tab.tabId}
			className={`scroll-mt-24 ${className}`}
		>
			<div className="bg-card rounded-xl shadow-md overflow-hidden p-4 sm:p-6">
				<h2 className="text-xl sm:text-2xl font-bold mb-4 text-foreground">
					{tab.title || tab.tabId}
				</h2>

				{/* 根据tab.type类型渲染不同内容 */}
				{tab.type === GameDetailContentType.RelatedVideos ? (
					<RelatedVideosContent gameVideos={gameVideos} />
				) : (
					<MarkdownContent tab={tab} />
				)}
			</div>
		</div>
	)
}

export default TabContentRenderer
