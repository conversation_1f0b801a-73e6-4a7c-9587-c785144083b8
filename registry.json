{"$schema": "https://ui.shadcn.com/schema/registry.json", "name": "nex<PERSON><PERSON>", "homepage": "https://nexoui.com", "items": [{"name": "accordion", "type": "registry:ui", "title": "Accordion", "description": "A vertically stacked set of interactive headings that each reveal a section of content.", "files": [{"path": "lib/components/ui/accordion.tsx", "type": "registry:component"}]}, {"name": "alert", "type": "registry:ui", "title": "<PERSON><PERSON>", "description": "Displays a callout for user attention.", "files": [{"path": "lib/components/ui/alert.tsx", "type": "registry:component"}]}, {"name": "alert-dialog", "type": "registry:ui", "title": "<PERSON><PERSON>", "description": "A modal dialog that interrupts the user with important content and expects a response.", "files": [{"path": "lib/components/ui/alert-dialog.tsx", "type": "registry:component"}]}, {"name": "avatar", "type": "registry:ui", "title": "Avatar", "description": "An image element with a fallback for representing the user.", "files": [{"path": "lib/components/ui/avatar.tsx", "type": "registry:component"}]}, {"name": "badge", "type": "registry:ui", "title": "Badge", "description": "Displays a badge or a component that looks like a badge.", "files": [{"path": "lib/components/ui/badge.tsx", "type": "registry:component"}]}, {"name": "button", "type": "registry:ui", "title": "<PERSON><PERSON>", "description": "Displays a button or a component that looks like a button.", "files": [{"path": "lib/components/ui/button.tsx", "type": "registry:component"}]}, {"name": "calendar", "type": "registry:ui", "title": "Calendar", "description": "A date field component that allows users to enter and edit date.", "files": [{"path": "lib/components/ui/calendar.tsx", "type": "registry:component"}]}, {"name": "calendar-picker", "type": "registry:ui", "title": "Calendar Picker", "description": "Enhanced calendar component with additional picking functionality.", "files": [{"path": "lib/components/ui/calendar-picker.tsx", "type": "registry:component"}]}, {"name": "card", "type": "registry:ui", "title": "Card", "description": "Displays a card with header, content, and footer.", "files": [{"path": "lib/components/ui/card.tsx", "type": "registry:component"}]}, {"name": "carousel", "type": "registry:ui", "title": "Carousel", "description": "A carousel component with controls and slide indicators.", "files": [{"path": "lib/components/ui/carousel.tsx", "type": "registry:component"}]}, {"name": "chart", "type": "registry:ui", "title": "Chart", "description": "A wrapper for Recharts library with common chart types.", "files": [{"path": "lib/components/ui/chart.tsx", "type": "registry:component"}]}, {"name": "checkbox", "type": "registry:ui", "title": "Checkbox", "description": "A control that allows the user to toggle between checked and not checked.", "files": [{"path": "lib/components/ui/checkbox.tsx", "type": "registry:component"}]}, {"name": "datetime-picker", "type": "registry:ui", "title": "Datetime Picker", "description": "A component to pick both date and time.", "files": [{"path": "lib/components/ui/datetime-picker.tsx", "type": "registry:component"}]}, {"name": "dialog", "type": "registry:ui", "title": "Dialog", "description": "A window overlaid on either the primary window or another dialog window.", "files": [{"path": "lib/components/ui/dialog.tsx", "type": "registry:component"}]}, {"name": "drawer", "type": "registry:ui", "title": "Drawer", "description": "A panel that slides out from the edge of the screen.", "files": [{"path": "lib/components/ui/drawer.tsx", "type": "registry:component"}]}, {"name": "dropdown-menu", "type": "registry:ui", "title": "Dropdown Menu", "description": "Displays a menu to the user — such as a set of actions or functions.", "files": [{"path": "lib/components/ui/dropdown-menu.tsx", "type": "registry:component"}]}, {"name": "form", "type": "registry:ui", "title": "Form", "description": "Advanced form components built on top of React Hook Form.", "files": [{"path": "lib/components/ui/form.tsx", "type": "registry:component"}]}, {"name": "input", "type": "registry:ui", "title": "Input", "description": "Displays a form input field or a component that looks like an input field.", "files": [{"path": "lib/components/ui/input.tsx", "type": "registry:component"}]}, {"name": "input-otp", "type": "registry:ui", "title": "Input OTP", "description": "A one-time password input component.", "files": [{"path": "lib/components/ui/input-otp.tsx", "type": "registry:component"}]}, {"name": "label", "type": "registry:ui", "title": "Label", "description": "Renders an accessible label associated with controls.", "files": [{"path": "lib/components/ui/label.tsx", "type": "registry:component"}]}, {"name": "pagination", "type": "registry:ui", "title": "Pagination", "description": "Navigation for paginated content.", "files": [{"path": "lib/components/ui/pagination.tsx", "type": "registry:component"}]}, {"name": "picker", "type": "registry:ui", "title": "Picker", "description": "A versatile picker component for mobile-friendly option selection.", "files": [{"path": "lib/components/ui/picker.tsx", "type": "registry:component"}]}, {"name": "popover", "type": "registry:ui", "title": "Popover", "description": "Displays rich content in a portal, triggered by a button.", "files": [{"path": "lib/components/ui/popover.tsx", "type": "registry:component"}]}, {"name": "progress", "type": "registry:ui", "title": "Progress", "description": "Displays an indicator showing the completion progress of a task.", "files": [{"path": "lib/components/ui/progress.tsx", "type": "registry:component"}]}, {"name": "pull-to-refresh", "type": "registry:ui", "title": "Pull to Refresh", "description": "A component that allows refreshing content by pulling down.", "files": [{"path": "lib/components/ui/pull-to-refresh.tsx", "type": "registry:component"}]}, {"name": "select", "type": "registry:ui", "title": "Select", "description": "Displays a list of options for the user to pick from—triggered by a button.", "files": [{"path": "lib/components/ui/select.tsx", "type": "registry:component"}]}, {"name": "sheet", "type": "registry:ui", "title": "Sheet", "description": "Extends the Dialog component to display content that complements the main content of the screen.", "files": [{"path": "lib/components/ui/sheet.tsx", "type": "registry:component"}]}, {"name": "sidebar", "type": "registry:ui", "title": "Sidebar", "description": "A comprehensive sidebar component with mobile responsiveness.", "files": [{"path": "lib/components/ui/sidebar.tsx", "type": "registry:component"}]}, {"name": "slider", "type": "registry:ui", "title": "Slide<PERSON>", "description": "An input where the user selects a value from within a given range.", "files": [{"path": "lib/components/ui/slider.tsx", "type": "registry:component"}]}, {"name": "switch", "type": "registry:ui", "title": "Switch", "description": "A control that allows the user to toggle between checked and not checked.", "files": [{"path": "lib/components/ui/switch.tsx", "type": "registry:component"}]}, {"name": "tabs", "type": "registry:ui", "title": "Tabs", "description": "A set of layered sections of content—known as tab panels—that display one panel of content at a time.", "files": [{"path": "lib/components/ui/tabs.tsx", "type": "registry:component"}]}, {"name": "textarea", "type": "registry:ui", "title": "Textarea", "description": "Displays a form textarea or a component that looks like a textarea.", "files": [{"path": "lib/components/ui/textarea.tsx", "type": "registry:component"}]}, {"name": "time-picker", "type": "registry:ui", "title": "Time Picker", "description": "A component to pick time.", "files": [{"path": "lib/components/ui/time-picker.tsx", "type": "registry:component"}]}, {"name": "time-range-picker", "type": "registry:ui", "title": "Time Range Picker", "description": "A component to pick time ranges.", "files": [{"path": "lib/components/ui/time-range-picker.tsx", "type": "registry:component"}]}, {"name": "toggle", "type": "registry:ui", "title": "Toggle", "description": "A two-state button that can be either on or off.", "files": [{"path": "lib/components/ui/toggle.tsx", "type": "registry:component"}]}, {"name": "toggle-group", "type": "registry:ui", "title": "Toggle Group", "description": "A set of two-state buttons that can be toggled on or off.", "files": [{"path": "lib/components/ui/toggle-group.tsx", "type": "registry:component"}]}, {"name": "tooltip", "type": "registry:ui", "title": "<PERSON><PERSON><PERSON>", "description": "A popup that displays information related to an element when the element receives keyboard focus or the mouse hovers over it.", "files": [{"path": "lib/components/ui/tooltip.tsx", "type": "registry:component"}]}, {"name": "uploader", "type": "registry:ui", "title": "Uploader", "description": "A file upload component with preview and drag-and-drop support.", "files": [{"path": "lib/components/ui/uploader.tsx", "type": "registry:component"}]}]}