import React from "react";
import { AdCode } from "@/lib/types/api-types";

interface ContentAdProps {
  position: string;
  adCodes: AdCode[];
  className?: string;
}

/**
 * 内容广告组件
 * 用于在内容中显示广告
 */
export const ContentAd: React.FC<ContentAdProps> = ({
  position,
  adCodes,
  className = "mt-6 mb-8 bg-accent/20 rounded-lg p-6 text-center border-2 border-accent/30",
}) => {
  // 获取指定位置的广告代码
  const getAdCode = (position: string) => {
    const ad = adCodes.find((ad) => ad.position === position);
    return ad ? ad.code : null;
  };

  return (
    <div
      className={className}
      dangerouslySetInnerHTML={{
        __html:
          getAdCode(position) ||
          `<div class="text-accent-foreground font-bold text-xl">广告位 - ${position}</div>`,
      }}
    />
  );
};

export default ContentAd;
