import React from "react"

interface GameHeaderProps {
	gameName: string
	gameSlogan: string
	className?: string
}

/**
 * 游戏标题和标语组件
 * 负责显示游戏的基本标题信息
 */
const GameHeader: React.FC<GameHeaderProps> = ({
	gameName,
	gameSlogan,
	className = ""
}) => {
	return (
		<div className={`mb-6 ${className}`}>
			<h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-3 text-foreground">
				{gameName}
			</h1>
			<p className="text-base sm:text-lg text-muted-foreground leading-relaxed">
				{gameSlogan}
			</p>
		</div>
	)
}

export default GameHeader
export { GameHeader }
