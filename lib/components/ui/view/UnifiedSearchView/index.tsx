"use client"

import React, { useState } from "react"
import { useRouter } from "next/navigation"
import { useTranslations } from "next-intl"
import { Search, Grid, List, Filter } from "lucide-react"
import { GameCard } from "@/lib/components/ui/view/GameCard"
import { GameListPagination } from "../GameListView/GameListPagination"
import { ProjectGame, GameLocaleContent } from "@/lib/types"

interface UnifiedSearchViewProps {
	query?: string
	results: any[]
	totalGames: number
	totalArticles: number
	totalResults: number
	currentPage: number
	totalPages: number
	currentType: "all" | "games" | "articles"
	locale: string
	resultsPerPage: number
}

export const UnifiedSearchView: React.FC<UnifiedSearchViewProps> = ({
	query = "",
	results,
	totalGames,
	totalArticles,
	totalResults,
	currentPage,
	totalPages,
	currentType,
	locale,
	resultsPerPage,
}) => {
	const t = useTranslations()
	const router = useRouter()
	const [searchQuery, setSearchQuery] = useState(query)
	const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

	// 处理搜索
	const handleSearch = (e: React.FormEvent) => {
		e.preventDefault()
		if (searchQuery.trim()) {
			router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`)
		}
	}

	// 处理类型切换
	const handleTypeChange = (type: "all" | "games" | "articles") => {
		const params = new URLSearchParams()
		if (query) {
			params.set("q", query)
		}
		if (type !== "all") {
			params.set("type", type)
		}

		const queryString = params.toString()
		const newURL = queryString ? `/search?${queryString}` : "/search"
		router.push(newURL)
	}

	// 处理分页
	const handlePageChange = (page: number) => {
		const params = new URLSearchParams()
		if (query) {
			params.set("q", query)
		}
		if (currentType !== "all") {
			params.set("type", currentType)
		}
		if (page > 1) {
			params.set("page", page.toString())
		}

		const queryString = params.toString()
		const newURL = queryString ? `/search?${queryString}` : "/search"
		router.push(newURL)
	}

	// 转换游戏数据为本地化内容
	const convertGameToLocaleContent = (
		game: ProjectGame,
	): GameLocaleContent & { isPrimary?: boolean } => {
		const gameLocale = game.gameLocales?.find(
			(gameLocale) => gameLocale.locale === locale,
		)

		if (!gameLocale) {
			return {
				id: game.id,
				slug: game.slug,
				gameInfo: game.gameInfo,
				categories: game.categories,
				tags: game.tags,
				gameImages: game.gameImages,
				gameName: game.name,
				gameSlogan: "",
				gameDescription: "",
				updateTime: new Date().toISOString(),
				metadata: {
					title: game.name,
					description: "",
				},
				contents: [],
				isPrimary: game.isPrimary,
			} as GameLocaleContent & { isPrimary?: boolean }
		}

		return {
			...gameLocale.content,
			id: game.id,
			slug: game.slug,
			gameInfo: game.gameInfo,
			categories: game.categories,
			tags: game.tags,
			gameImages: game.gameImages,
			isPrimary: game.isPrimary,
		} as GameLocaleContent & { isPrimary?: boolean }
	}

	return (
		<div className="space-y-6">
			{/* 搜索框 */}
			<form onSubmit={handleSearch} className="relative">
				<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
				<input
					type="text"
					placeholder={t("Search.searchPlaceholder")}
					value={searchQuery}
					onChange={(e) => setSearchQuery(e.target.value)}
					className="w-full pl-10 pr-20 py-3 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
				/>
				<button
					type="submit"
					className="absolute right-2 top-1/2 transform -translate-y-1/2 px-4 py-1.5 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
				>
					{t("Search.searchButton")}
				</button>
			</form>

			{/* 结果类型切换 */}
			{query && (
				<div className="flex flex-wrap items-center gap-4">
					<div className="flex items-center gap-2">
						<button
							type="button"
							onClick={() => handleTypeChange("all")}
							className={`px-4 py-2 rounded-lg text-sm font-medium ${
								currentType === "all"
									? "bg-primary text-primary-foreground"
									: "bg-muted text-muted-foreground hover:bg-muted/80"
							}`}
						>
							{t("Search.allResults")} ({totalResults})
						</button>
						<button
							type="button"
							onClick={() => handleTypeChange("games")}
							className={`px-4 py-2 rounded-lg text-sm font-medium ${
								currentType === "games"
									? "bg-primary text-primary-foreground"
									: "bg-muted text-muted-foreground hover:bg-muted/80"
							}`}
						>
							{t("Search.games")} ({totalGames})
						</button>
						<button
							type="button"
							onClick={() => handleTypeChange("articles")}
							className={`px-4 py-2 rounded-lg text-sm font-medium ${
								currentType === "articles"
									? "bg-primary text-primary-foreground"
									: "bg-muted text-muted-foreground hover:bg-muted/80"
							}`}
						>
							{t("Search.articles")} ({totalArticles})
						</button>
					</div>

					{/* 视图模式切换 - 仅在游戏结果时显示 */}
					{(currentType === "games" ||
						(currentType === "all" && totalGames > 0)) && (
						<div className="flex items-center gap-2 ml-auto">
							<button
								type="button"
								onClick={() => setViewMode("grid")}
								className={`p-2 rounded-lg ${
									viewMode === "grid"
										? "bg-primary text-primary-foreground"
										: "bg-background text-foreground hover:bg-muted"
								}`}
							>
								<Grid className="h-4 w-4" />
							</button>
							<button
								type="button"
								onClick={() => setViewMode("list")}
								className={`p-2 rounded-lg ${
									viewMode === "list"
										? "bg-primary text-primary-foreground"
										: "bg-background text-foreground hover:bg-muted"
								}`}
							>
								<List className="h-4 w-4" />
							</button>
						</div>
					)}
				</div>
			)}

			{/* 搜索结果 */}
			{query ? (
				results.length > 0 ? (
					<div className="space-y-6">
						{/* 游戏结果 */}
						{currentType === "games" ? (
							<div
								className={
									viewMode === "grid"
										? "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4"
										: "space-y-4"
								}
							>
								{results.map((game: ProjectGame) => {
									const gameContent = convertGameToLocaleContent(game)
									return <GameCard key={game.id} {...gameContent} />
								})}
							</div>
						) : currentType === "articles" ? (
							<div className="space-y-4">
								{results.map((article: any, index: number) => (
									<div
										key={index}
										className="p-4 border border-border rounded-lg bg-card"
									>
										<h3 className="text-lg font-semibold mb-2">文章标题</h3>
										<p className="text-muted-foreground">文章摘要...</p>
									</div>
								))}
							</div>
						) : (
							// 混合结果
							<div className="space-y-6">
								{results.map((result: any, index: number) => (
									<div key={index}>
										{result.type === "game" ? (
											<div className="space-y-2">
												{index === 0 && (
													<h2 className="text-xl font-semibold">
														{t("Search.games")}
													</h2>
												)}
												<div
													className={
														viewMode === "grid"
															? "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4"
															: "space-y-4"
													}
												>
													{(() => {
														const gameContent = convertGameToLocaleContent(
															result.data,
														)
														return <GameCard {...gameContent} />
													})()}
												</div>
											</div>
										) : (
											<div className="space-y-2">
												{index === 0 && (
													<h2 className="text-xl font-semibold">
														{t("Search.articles")}
													</h2>
												)}
												<div className="p-4 border border-border rounded-lg bg-card">
													<h3 className="text-lg font-semibold mb-2">
														文章标题
													</h3>
													<p className="text-muted-foreground">文章摘要...</p>
												</div>
											</div>
										)}
									</div>
								))}
							</div>
						)}

						{/* 分页 */}
						{totalPages > 1 && (
							<GameListPagination
								currentPage={currentPage}
								totalPages={totalPages}
								onPageChange={handlePageChange}
							/>
						)}
					</div>
				) : (
					<div className="text-center py-12 bg-muted/50 rounded-lg">
						<p className="text-muted-foreground text-lg">
							{t("Search.noResults", { query })}
						</p>
					</div>
				)
			) : (
				<div className="text-center py-12 bg-muted/50 rounded-lg">
					<p className="text-muted-foreground text-lg">
						{t("Search.enterQuery")}
					</p>
				</div>
			)}
		</div>
	)
}
