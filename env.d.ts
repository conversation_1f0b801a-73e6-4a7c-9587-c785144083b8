import type { PrismaClient } from "@prisma/client"

declare global {
	// 全局持有 Prisma 避免在开发环境下Prisma 链接未关闭导致的报错
	var prisma: PrismaClient | undefined

	namespace NodeJS {
		interface ProcessEnv {
			// Add any custom process.env variables here
			// NEXT_PUBLIC_API_URL: string;
			S3_ACCESS_KEY: string
			S3_SECRET_KEY: string
			S3_SESSION_TOKEN: string
			S3_ENDPOINT: string
			S3_ACCOUNT_ID: string
			S3_PUBLIC_URL: string
			S3_REGION: string
		}
	}
}
