"use client"
import React from "react"
import MarkdownRenderer from "@/lib/components/common/MarkdownRenderer"
import { GameDetailContent } from "@/lib/types/api-types"

interface MarkdownContentProps {
	tab: GameDetailContent
}

const MarkdownContent: React.FC<MarkdownContentProps> = ({ tab }) => {
	return (
		<MarkdownRenderer
			content={tab.text || ""}
			className="prose dark:prose-invert prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground prose-ul:text-foreground prose-li:text-foreground prose-a:text-primary prose-img:my-4 max-w-none"
			variant={tab.tabId === "faq" ? "faq" : "default"}
		/>
	)
}

export default MarkdownContent
