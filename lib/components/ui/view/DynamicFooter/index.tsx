"use client"
import { Icon } from "@/lib/components/common"
import {
	SiteSettings,
	FriendLink,
	MetadataInfo,
	GameLocaleContent,
} from "@/lib/types"
import { Link, usePathname, cleanPathname } from "@i18n/navigation"
import { useTranslations, useLocale } from "next-intl"
import React, { useEffect, useState } from "react"
import { checkProperty } from "@/lib/utils/react"
import { Languages } from "lucide-react"

interface DynamicFooterProps {
	siteSettings?: SiteSettings
	siteMetadata?: MetadataInfo
	popularGames?: GameLocaleContent[]
}

export const DynamicFooter: React.FC<DynamicFooterProps> = ({
	siteSettings,
	siteMetadata,
	popularGames,
}) => {
	const t = useTranslations("Footer")
	const locale = useLocale()
	const pathname = usePathname()
	const [showConsent, setShowConsent] = useState(false)

	// Cookie同意处理
	useEffect(() => {
		// 检查是否已经同意Cookie
		const hasConsent = localStorage.getItem("cookieConsent")
		if (!hasConsent) {
			setShowConsent(true)
		}
	}, [])

	const handleConsent = (type: "all" | "necessary") => {
		localStorage.setItem("cookieConsent", type)
		setShowConsent(false)
	}

	// 渲染快速链接
	const renderQuickLink = (link: FriendLink) => {
		return (
			<li key={link.name}>
				<Link
					href={link.url}
					className="hover:text-primary-foreground/80 transition-colors"
				>
					{link.name}
				</Link>
			</li>
		)
	}

	// 渲染热门游戏链接
	const renderPopularGame = (game: GameLocaleContent) => {
		return (
			<li key={game.id}>
				<Link
					href={`/games/${game.slug}`}
					className="hover:text-primary-foreground/80 transition-colors"
				>
					{game.gameName}
				</Link>
			</li>
		)
	}

	// 渲染社交媒体图标
	const renderSocialIcon = (platform: string, url?: string) => {
		if (!url) return null
		return (
			<a
				href={url}
				target="_blank"
				rel="noopener noreferrer"
				aria-label={platform}
				className="text-primary-foreground/70 hover:text-primary-foreground transition-colors"
			>
				<Icon name={platform.toLowerCase()} size={20} />
			</a>
		)
	}

	// 语言国旗映射
	const flagMap: Record<string, string> = {
		en: "🇺🇸", // 英语 - 美国
		de: "🇩🇪", // 德语 - 德国
		fr: "🇫🇷", // 法语 - 法国
		ja: "🇯🇵", // 日语 - 日本
		ko: "🇰🇷", // 韩语 - 韩国
		"zh-CN": "🇨🇳", // 简体中文 - 中国
		"zh-TW": "🇭🇰", // 繁体中文 - 香港
		"es-ES": "🇪🇸", // 西班牙语 - 西班牙
		it: "🇮🇹", // 意大利语 - 意大利
		nl: "🇳🇱", // 荷兰语 - 荷兰
		"pt-PT": "🇵🇹", // 葡萄牙语 - 葡萄牙
	}

	return (
		<footer className="bg-primary dark:bg-slate-800 text-primary-foreground pt-12 pb-6 mt-auto">
			<div className="container mx-auto px-4 sm:px-6">
				{/* 上部分 - 四列布局 */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
					{/* 网站信息 */}
					<div>
						<div className="flex items-center mb-4">
							<Icon
								name={siteSettings?.logo || ""}
								alt={siteSettings?.siteName || ""}
								className="h-8 w-8 mr-2 text-primary-foreground/80"
								size={32}
							/>
							<h2 className="text-xl font-bold text-primary-foreground">
								{siteSettings?.siteName}
							</h2>
						</div>
						<p className="mb-4 text-sm leading-relaxed text-primary-foreground/80">
							{siteMetadata?.description}
						</p>
					</div>

					{/* 热门游戏 - 按需渲染 */}
					{popularGames && popularGames.length > 0 ? (
						<div>
							<h3 className="text-lg font-semibold text-primary-foreground mb-4">
								{t("popularGames")}
							</h3>
							<ul className="space-y-2 text-primary-foreground/80">
								{popularGames.slice(0, 5).map(renderPopularGame)}
							</ul>
						</div>
					) : (
						<div></div>
					)}

					{/* 友情链接 - 按需渲染 */}
					{siteSettings?.friendLinks && siteSettings.friendLinks.length > 0 ? (
						<div>
							<h3 className="text-lg font-semibold text-primary-foreground mb-4">
								{t("friendLinks")}
							</h3>
							<ul className="space-y-2 text-primary-foreground/80">
								{siteSettings.friendLinks.map(renderQuickLink)}
							</ul>
						</div>
					) : (
						<div></div>
					)}

					{/* 联系我们 */}
					{siteSettings?.contactEmail && (
						<div>
							<h3 className="text-lg font-semibold text-primary-foreground mb-4">
								{t("contactUs")}
							</h3>
							<div className="flex items-center mb-3 text-primary-foreground/80">
								<Icon name="mail" className="mr-2" size={16} />
								<a
									href={`mailto:${siteSettings.contactEmail}`}
									className="hover:text-primary-foreground transition-colors"
								>
									{siteSettings.contactEmail}
								</a>
							</div>
							{/* 社交媒体链接 */}
							{siteSettings?.socialLinks && (
								<div className="flex space-x-4 mt-3">
									{renderSocialIcon(
										"Twitter",
										siteSettings.socialLinks.twitter,
									)}
									{renderSocialIcon(
										"Facebook",
										siteSettings.socialLinks.facebook,
									)}
									{renderSocialIcon(
										"Instagram",
										siteSettings.socialLinks.instagram,
									)}
									{renderSocialIcon(
										"Youtube",
										siteSettings.socialLinks.youtube,
									)}
									{renderSocialIcon(
										"Linkedin",
										siteSettings.socialLinks.linkedin,
									)}
								</div>
							)}
						</div>
					)}
				</div>

				{/* 下部分 - 版权信息和多语言链接 */}
				<div className="border-t border-border/50 pt-6 text-sm flex flex-col sm:flex-row justify-between items-center">
					{/* 版权声明 */}
					<p className="text-primary-foreground/70 mb-4 sm:mb-0">
						&copy; {new Date().getFullYear()} {siteSettings?.siteName}.{" "}
						{t("allRightsReserved")}
					</p>

					{/* 多语言链接 */}
					{siteSettings?.languanges && siteSettings.languanges.length > 1 && (
						<div className="flex items-center space-x-3">
							<Languages className="h-4 w-4 text-primary-foreground/70" />
							{siteSettings.languanges.map((lang) => (
								<Link
									key={lang}
									href={cleanPathname(lang, pathname) || "/"}
									locale={lang}
									className={`flex items-center text-primary-foreground/70 hover:text-primary-foreground transition-colors ${locale === lang ? "font-bold" : ""}`}
								>
									<span className="mr-1">{flagMap[lang] ?? "🏳️"}</span>
									{siteSettings.languangesNames[lang]?.localName || lang}
								</Link>
							))}
						</div>
					)}
				</div>
			</div>

			{/* Cookie同意提示 */}
			{showConsent && (
				<div className="fixed bottom-0 left-0 right-0 bg-card p-4 z-50 shadow-lg transition-opacity duration-300 opacity-100 text-card-foreground">
					<div className="container mx-auto flex flex-col sm:flex-row items-center justify-between gap-4">
						<div className="text-sm text-center sm:text-left">
							{t("cookieConsentText")}{" "}
							<Link
								href="/cookies"
								className="text-primary hover:text-primary/80 underline ml-1"
							>
								{t("learnMore")}
							</Link>
						</div>
						<div className="flex space-x-3 flex-shrink-0">
							<button
								type="button"
								onClick={() => handleConsent("all")}
								className="bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-2 rounded-lg text-sm font-medium transition-colors"
							>
								{t("acceptAll")}
							</button>
							<button
								type="button"
								onClick={() => handleConsent("necessary")}
								className="bg-secondary hover:bg-secondary/90 text-secondary-foreground px-4 py-2 rounded-lg text-sm font-medium transition-colors"
							>
								{t("acceptNecessary")}
							</button>
						</div>
					</div>
				</div>
			)}
		</footer>
	)
}
