import { SiteSettings } from '@/lib/types';
import fs from 'fs';
import path from 'path';

interface ThemeConfigGeneratorProps {
  siteSettings: SiteSettings;
}

/**
 * 可用的主题列表
 */
const AVAILABLE_THEMES = ['default', 'green', 'scheme1', 'scheme2', 'scheme3', 'scheme4', 'scheme5', 'scheme6'];

/**
 * 主题文件映射
 */
const THEME_FILES: Record<string, string> = {
  'default': '../../lib/themes/default-theme.css',
  'green': '../../lib/themes/green-theme.css',
  'scheme1': '../../lib/themes/scheme1-theme.css', // 深邃蓝
  'scheme2': '../../lib/themes/scheme2-theme.css', // 赛博朋克
  'scheme3': '../../lib/themes/scheme3-theme.css', // 暗夜模式
  'scheme4': '../../lib/themes/scheme4-theme.css', // 活力橙
  'scheme5': '../../lib/themes/scheme5-theme.css', // 森林绿
  'scheme6': '../../lib/themes/scheme6-theme.css', // 霓虹粉
};

/**
 * 服务端主题配置生成器
 *
 * 根据 siteConfig 中的主题配置，生成主题导入文件
 * 这个组件只在构建时运行，不会在客户端渲染
 */
export async function ThemeConfigGenerator({ siteSettings }: ThemeConfigGeneratorProps) {
  // 只在服务端运行
  if (typeof window !== 'undefined') {
    return null;
  }

  // 获取主题配置，如果没有则使用默认主题
  const themeName = siteSettings.theme?.name && AVAILABLE_THEMES.includes(siteSettings.theme?.name)
    ? siteSettings.theme?.name
    : 'default';

  // 获取对应的主题文件
  const themeFile = THEME_FILES[themeName];

  // 生成主题导入文件内容
  const themeImportContent = `/*
 * 自动生成的主题导入文件
 * 主题: ${themeName}
 * 文件: ${themeFile}
 */
@import '${themeFile}';
`;

  try {
    // 在构建时生成主题导入文件
    const themeImportPath = path.join(process.cwd(), 'app/[locale]/current-theme.css');
    fs.writeFileSync(themeImportPath, themeImportContent);
    console.log('Theme import file generated:', themeImportPath);
    console.log(`Using theme: ${themeName} (${themeFile})`);
  } catch (error) {
    console.error('Failed to generate theme import file:', error);
  }

  // 这个组件不渲染任何内容
  return null;
}

export default ThemeConfigGenerator;
