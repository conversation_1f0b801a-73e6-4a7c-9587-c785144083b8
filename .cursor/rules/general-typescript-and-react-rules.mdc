---
description: 为整个项目的 TypeScript 和 React 开发应用通用编码原则与最佳实践。
globs: **/*.ts,**/*.tsx
alwaysApply: false
---
- 编写简洁且易读的 TypeScript 代码。
- 使用函数式和声明式编程模式。
- 遵循 DRY（不要重复你自己）原则。
- 通过提前 return 提升代码可读性。
- 合理组织组件结构：导出、子组件、辅助方法、类型。
- 使用带有助动词的描述性命名（如 isLoading、hasError）。
- 事件处理函数统一以 'handle' 前缀命名（如 handleClick、handleSubmit）。
- 代码开发优先采用函数式编程范式，优先导出具名函数。
- 所有代码均使用 TypeScript。
- 优先使用 interface 而非 type。
- 避免使用枚举，推荐使用 const map 替代。
- 实现严格的类型安全与类型推断。
- 类型校验时使用 `satisfies` 操作符。
- 在 tsconfig.json 中启用所有严格模式选项
- 为所有变量、参数和返回值显式声明类型
- 使用工具类型、映射类型和条件类型
- 可扩展对象结构优先使用 'interface'
- 联合类型、交叉类型和基础类型组合优先使用 'type'
- 用 JSDoc 为复杂类型添加文档说明
- 避免使用不明确的联合类型，必要时使用可区分联合类型
- 优先检索项目中的`consts`目录下常量或枚举。作为状态、类型、角色等类似值（如有需求新增定义）
