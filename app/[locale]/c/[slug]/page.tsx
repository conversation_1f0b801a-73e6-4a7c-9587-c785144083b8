import { <PERSON>ada<PERSON> } from "next"
import { setRequestLocale, getTranslations } from "next-intl/server"
import { locales as apiLocales } from "@/lib/i18n/locales"
import { Breadcrumb } from "@/lib/components/ui/view/Breadcrumb"
import { GameCard } from "@/lib/components/ui/view/GameCard"

import {
	getGameCategoriesByLocale,
	getGameCategoryDetailByLocale,
	getGames,
	getHomePageMetadata,
} from "@/lib/services/api-client"
import {
	BreadcrumbItem,
	GameLocaleContent,
} from "@/lib/types/api-types"
import { getGameLocaleContent } from "@/lib/services/api-client"
import { notFound } from "next/navigation"

export const dynamic = 'force-static'

type Props = {
	params: Promise<{ locale: string; slug: string }>
}

export async function generateStaticParams(): Promise<
	{ locale: string; slug: string }[]
> {
	const params: { locale: string; slug: string }[] = []

	for (const locale of apiLocales) {
		try {
			const categories = await getGameCategoriesByLocale(locale)
			for (const category of categories) {
				if (category.slug) {
					params.push({
						locale,
						slug: category.slug,
					})
				}
			}
		} catch (error) {
			console.error(`Error generating static params for locale ${locale}:`, error)
		}
	}

	return params
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ locale: string; slug: string }>
}): Promise<Metadata> {
	const { locale, slug } = await params
	const category = await getGameCategoryDetailByLocale(locale, slug)
	const homeMetadata = await getHomePageMetadata(locale).catch(() => null)

	if (!category || !category.name) {
		return {
			title: "Category Not Found",
			description: "The requested category could not be found.",
		}
	}

	const title = category.metadata?.title || category.name
	const description = category.metadata?.description || `Explore ${category.name} games`

	return {
		title,
		description,
		openGraph: {
			title: category.metadata?.ogTitle || title,
			description: category.metadata?.ogDescription || description,
			...(homeMetadata?.ogImage ? { images: [homeMetadata.ogImage] } : {}),
		},
	}
}

export default async function CategoryPage({
	params,
}: Props) {
	const { locale, slug } = await params

	setRequestLocale(locale)

	const t = await getTranslations("Common")

	// 获取分类详情
	const category = await getGameCategoryDetailByLocale(locale, slug)

	if (!category || !category.name) {
		notFound()
	}

	// 获取所有游戏
	const allGames = await getGames()

	// 筛选当前分类下的游戏
	const categoryGames = allGames.filter(game =>
		game.categories?.includes(category.code)
	)

	// 转换为本地化内容
	const gamesWithLocaleContent: GameLocaleContent[] = categoryGames
		.map(game => {
			try {
				return getGameLocaleContent(locale, game)
			} catch (error) {
				console.warn(`Could not get locale content for game ${game.name}:`, error)
				return null
			}
		})
		.filter((game): game is GameLocaleContent => game !== null && !!game.id)

	const breadcrumbItems: BreadcrumbItem[] = [
		{ label: t("Home"), href: "/" },
		{ label: t("Category"), href: "/categories" },
		{ label: category.name, href: `/c/${slug}` },
	]

	return (
		<div className="container mx-auto px-4 py-8">
			<Breadcrumb items={breadcrumbItems} />

			<header className="mb-8 text-center">
				<h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
					{category.name}
				</h1>
				{category.metadata?.description && (
					<p className="mt-2 max-w-2xl mx-auto text-base text-gray-500 dark:text-gray-400">
						{category.metadata.description}
					</p>
				)}
				<p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
					{gamesWithLocaleContent.length} {gamesWithLocaleContent.length === 1 ? "game" : "games"} found
				</p>
			</header>

			{/* 游戏网格 */}
			{gamesWithLocaleContent.length > 0 ? (
				<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 mb-8">
					{gamesWithLocaleContent.map((game) => (
						<GameCard key={game.id} {...game} />
					))}
				</div>
			) : (
				<div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg mb-8">
					<p className="text-gray-500 dark:text-gray-400 text-lg">
						No games found in this category
					</p>
				</div>
			)}

		</div>
	)
}
