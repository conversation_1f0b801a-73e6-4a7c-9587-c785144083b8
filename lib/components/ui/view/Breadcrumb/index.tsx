'use client'
import React from 'react';
import {Link} from '@i18n/navigation';
import { Icon } from '@/lib/components/common';
import { BreadcrumbItem } from '@/lib/types';

interface BreadcrumbProps {
  items: BreadcrumbItem[];
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({ items }) => {
  if(!items || items.length === 0){
    return null
  }
  return (
    <div className="bg-muted py-2 border-b border-border">
      <div className="container mx-auto px-4">
        <div className="flex items-center text-sm text-muted-foreground">
          {items.map((item, index) => {
            const isLast = index === items.length - 1;

            return (
              <React.Fragment key={index}>
                {item.href && !item.isActive ? (
                  <Link
                    href={item.href}
                    className="hover:text-primary"
                  >
                    {item.label}
                  </Link>
                ) : (
                  <span className={`${item.isActive ? 'font-bold text-primary' : ''}`}>
                    {item.label}
                  </span>
                )}

                {!isLast && (
                  <Icon name="ChevronRight" className="h-3 w-3 mx-2" size={12} />
                )}
              </React.Fragment>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Breadcrumb;
