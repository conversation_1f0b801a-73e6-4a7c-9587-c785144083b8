import React from "react"
import { <PERSON> } from "@i18n/navigation"
import { GameTag, ProjectGame } from "@/lib/types/api-types"
import { useTranslations } from "next-intl"

interface GameInfoSectionProps {
	projectGame: ProjectGame
	tags: GameTag[]
	className?: string
}

/**
 * 游戏信息段落组件
 * 显示游戏详细信息和标签
 */
const GameInfoSection: React.FC<GameInfoSectionProps> = ({
	projectGame,
	tags,
	className = ""
}) => {
	const t = useTranslations("Game")

	// 定义游戏信息项目的映射关系
	const gameInfoMapping = [
		{ key: "developer", label: "Developer", renderSpecial: false },
		{ key: "rating", label: "Rating", renderSpecial: true, renderType: "stars" },
		{ key: "releaseDate", label: "Release Date", renderSpecial: false },
		{ key: "technology", label: "Technology", renderSpecial: false },
		{ key: "platform", label: "Platforms", renderSpecial: true, renderType: "platform" },
		{ key: "ageRating", label: "Age Rating", renderSpecial: false },
		{ key: "localization", label: "Localization", renderSpecial: false },
		{ key: "screenOrientation", label: "Screen Orientation", renderSpecial: false },
		{ key: "cloudSaves", label: "Cloud Saves", renderSpecial: false },
		{ key: "authorizationSupport", label: "Authorization Support", renderSpecial: false }
	]

	// 游戏分类
	const categories = [
		{ name: "All Games", href: "/games" },
		{ name: "Puzzles", href: "/games/puzzles" },
		{ name: "Merge Italian Brainrot", href: "#" }
	]

	// 渲染星级评分
	const renderStars = (rating: number) => {
		const fullStars = Math.floor(rating)
		const emptyStars = 5 - fullStars
		const stars = '★'.repeat(fullStars) + '☆'.repeat(emptyStars)
		return (
			<div className="flex items-center">
				<div className="stars text-amber-400 mr-2">
					{stars}
				</div>
				<span>{rating} (Player Rating)</span>
			</div>
		)
	}

	// 渲染平台图标
	const renderPlatformIcons = (platformValue: string) => {
		if (!platformValue) return null
		
		const platforms = platformValue.split(/,\s*/) // 分割平台字符串
		return (
			<div className="flex items-center flex-wrap gap-1">
				{platforms.map((platform, idx) => {
					const icon = platform.trim().charAt(0).toUpperCase() // 获取平台首字母作为图标
					return (
						<React.Fragment key={idx}>
							<span className="platform-icon bg-gray-200 text-gray-700 rounded px-1">{icon}</span>
							<span className="mr-2">{platform.trim()}{idx < platforms.length - 1 ? "," : ""}</span>
						</React.Fragment>
					)
				})}
			</div>
		)
	}

	return (
		<div className={`game-info-section ${className}`}>
			{/* 游戏详细信息 */}
			{projectGame.gameInfo.settings && (
				<div className="game-details-container space-y-4">
					{gameInfoMapping.map((item, index) => {
						// 从游戏设置中获取对应的值
						const value = projectGame.gameInfo.settings[item.key]
						
						// 如果值为空，则跳过此项
						if (value === undefined || value === null || value === '') return null
						
						return (
							<div key={index} className="game-info-item flex">
								<div className="info-label w-1/3 text-muted-foreground font-medium">
									{item.label}:
								</div>
								<div className="info-value w-2/3 text-foreground">
									{item.renderSpecial ? (
										item.renderType === "stars" ? (
											renderStars(value as number)
										) : item.renderType === "platform" ? (
											renderPlatformIcons(value as string)
										) : (
											<span>{value}</span>
										)
									) : (
										<span>{value}</span>
									)}
								</div>
							</div>
						)
					})}
				</div>
			)}

			{/* 游戏分类 */}
			<div className="game-categories mt-6">
				<h3 className="text-lg font-semibold mb-3 text-foreground">
					{t("categories") || "分类"}
				</h3>
				<div className="categories-path flex items-center text-sm">
					{categories.map((category, index) => (
						<React.Fragment key={index}>
							<Link 
								href={category.href}
								className="text-primary hover:underline"
							>
								{category.name}
							</Link>
							{index < categories.length - 1 && (
								<span className="mx-2 text-muted-foreground">{'>'}</span>
							)}
						</React.Fragment>
					))}
				</div>
			</div>

			{/* 游戏标签 */}
			<div className="game-tags mt-6">
				<h3 className="text-lg font-semibold mb-3 text-foreground">
					{t("tags") || "标签"}
				</h3>
				<div className="tags-container flex flex-wrap gap-2">
					{tags && tags.length > 0 ? (
						tags.map((tag) => (
							<Link
								key={tag.id}
								href={tag.slug}
								className="tag-item bg-primary/10 text-primary px-3 py-1.5 rounded-lg text-sm hover:bg-primary/20 transition-colors border border-primary/20 font-medium"
							>
								{tag.name}
							</Link>
						))
					) : null}
				</div>
			</div>
		</div>
	)
}

export default GameInfoSection
export { GameInfoSection }
