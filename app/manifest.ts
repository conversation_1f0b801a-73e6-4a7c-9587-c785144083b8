import type { MetadataRoute } from "next"
import { getSiteSettings } from "@/lib/services/api-client"

export default async function manifest(): Promise<MetadataRoute.Manifest> {
	// 获取网站设置以使用实际的网站信息
	const siteSettings = await getSiteSettings()

	// 使用网站设置中的图标，如果存在的话
	const icons: MetadataRoute.Manifest["icons"] = []

	// 添加标准 PWA 图标
	if (siteSettings.icons?.favicon) {
		icons.push({
			src: siteSettings.icons.favicon,
			sizes: "32x32",
			type: "image/png",
		})
	}

	if (siteSettings.icons?.androidIcon) {
		icons.push({
			src: siteSettings.icons.androidIcon,
			sizes: "192x192",
			type: "image/png",
		})

		// 添加 512x512 大小的图标，假设与 androidIcon 使用相同的图片
		icons.push({
			src: siteSettings.icons.androidIcon,
			sizes: "512x512",
			type: "image/png",
		})

		// 添加带有 purpose 的图标，用于 maskable 图标
		icons.push({
			src: siteSettings.icons.androidIcon,
			sizes: "512x512",
			type: "image/png",
			purpose: "maskable",
		})
	}

	// 如果没有设置图标，使用默认图标
	if (icons.length === 0) {
		icons.push(
			{
				src: "/icon-192x192.png",
				sizes: "192x192",
				type: "image/png",
			},
			{
				src: "/icon-512x512.png",
				sizes: "512x512",
				type: "image/png",
			},
			{
				src: "/icon-512x512.png",
				sizes: "512x512",
				type: "image/png",
				purpose: "maskable",
			},
		)
	}

	return {
		name: siteSettings.siteName || "七指鹿",
		short_name: siteSettings.siteName || "七指鹿",
		description: siteSettings.siteName || "七指鹿游戏平台",
		start_url: "/",
		display: "standalone",
		background_color: "#ffffff",
		theme_color: "#000000",
		orientation: "portrait",
		scope: "/",
		lang: "zh",
		prefer_related_applications: false,
		icons: icons,
	}
}
