import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';

// 自定义组件，用于在ReactMarkdown中渲染图片
const MarkdownImage = ({ node, ...props }: any) => {
  return (
    <img 
      {...props} 
      className="rounded-lg shadow-md my-4 max-w-full h-auto" 
      loading="lazy"
    />
  );
};

interface MarkdownRendererProps {
  content: string;
  className?: string;
  variant?: 'default' | 'faq';
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ 
  content, 
  className = "prose dark:prose-invert prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground prose-a:text-primary prose-img:my-4 max-w-none",
  variant = 'default'
}) => {
  return (
    <div className={className}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={{
          img: MarkdownImage,
          h1: ({node, ...props}) => <h1 className="text-2xl font-bold mt-4 mb-2" {...props} />,
          h2: ({node, ...props}) => <h2 className="text-xl font-bold mt-4 mb-2 text-primary" {...props} />,
          h3: ({node, ...props}) => <h3 className={`text-lg font-bold ${variant === 'faq' ? 'text-primary mt-6' : 'mt-3 mb-2'}`} {...props} />,
          p: ({node, children, ...props}) => {
            // 检查是否是FAQ答案（以"A:"开头）
            if (variant === 'faq') {
              const content = children?.toString() || '';
              if (content.startsWith('A:') || content.startsWith('A：')) {
                return <p className="mt-2" {...props}>{children}</p>;
              }
            }
            return <p className="mb-4" {...props}>{children}</p>;
          },
          ul: ({node, ...props}) => <ul className="list-disc pl-6 mb-4 space-y-1" {...props} />,
          li: ({node, ...props}) => <li className="mb-1" {...props} />,
          a: ({node, ...props}) => <a className="text-primary hover:underline" {...props} />
        }}
      >
        {content || ''}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer;
