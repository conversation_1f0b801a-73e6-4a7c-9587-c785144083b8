# 图标系统使用指南

本文档介绍了项目中的图标系统，包括如何使用内置图标、自定义图标以及与API集成。

## 1. 基本用法

### 使用 Icon 组件

```tsx
import { Icon } from '@/lib/components/common';
import { IconType } from '@/lib/types/icon';

// 使用 Lucide 图标（默认）
<Icon name="Home" />

// 指定大小和颜色
<Icon name="Settings" size={32} color="blue" />

// 使用 SVG 图标
<Icon 
  name="custom-icon" 
  type={IconType.SVG} 
  src="/path/to/icon.svg" 
/>

// 使用图片图标
<Icon 
  name="logo" 
  type={IconType.IMAGE} 
  src="/path/to/logo.png" 
/>
```

### 图标类型

系统支持以下图标类型：

- `IconType.LUCIDE`: Lucide 图标库（默认）
- `IconType.SVG`: SVG 图标
- `IconType.IMAGE`: 图片图标
- `IconType.COMPONENT`: 自定义组件图标

## 2. 与 API 集成

系统提供了辅助函数，可以根据 API 返回的数据自动选择合适的图标。

### 解析 API 返回的图标名称

```tsx
import { getIconFromApi } from '@/lib/utils/icon-helpers';
import { Icon } from '@/lib/components/common';

// API 返回的图标名称
const apiIconName = 'game-info';

// 解析图标名称
const iconInfo = getIconFromApi(apiIconName);

// 使用解析后的图标
<Icon 
  name={iconInfo.name} 
  type={iconInfo.type} 
  src={iconInfo.path} 
/>
```

### 支持的图标名称格式

系统支持多种格式的图标名称：

1. **普通名称**: `"Home"`, `"Settings"`, `"game-info"`
2. **带类型前缀**: `"lucide:Home"`, `"svg:/icons/custom.svg"`, `"img:/images/logo.png"`
3. **完整路径**: `"/icons/custom.svg"`, `"https://example.com/icon.png"`

### 自动分类图标

```tsx
import { getCategoryIcon } from '@/lib/utils/icon-helpers';
import { Icon } from '@/lib/components/common';

// 根据分类名称获取图标
const iconName = getCategoryIcon('action');

// 使用分类图标
<Icon name={iconName} />
```

## 3. 图标选择器

系统提供了图标选择器组件，可以在界面上浏览和选择图标。

```tsx
import { useState } from 'react';
import { IconPicker } from '@/lib/components/common';
import { IconType } from '@/lib/types/icon';

function MyComponent() {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState('Home');
  const [selectedType, setSelectedType] = useState(IconType.LUCIDE);

  const handleSelectIcon = (name, type) => {
    setSelectedIcon(name);
    setSelectedType(type);
    setIsOpen(false);
  };

  return (
    <>
      <button onClick={() => setIsOpen(true)}>
        选择图标
      </button>
      
      <IconPicker
        open={isOpen}
        onOpenChange={setIsOpen}
        onSelect={handleSelectIcon}
        selectedIcon={selectedIcon}
        selectedType={selectedType}
      />
    </>
  );
}
```

## 4. 图标钩子

系统提供了 `useIcon` 钩子，简化图标相关操作。

```tsx
import { useIcon } from '@/lib/hooks/useIcon';
import { Icon } from '@/lib/components/common';

function MyComponent() {
  const {
    selectedIcon,
    selectedType,
    isPickerOpen,
    openIconPicker,
    closeIconPicker,
    selectIcon,
    iconExists,
    searchIcons
  } = useIcon();

  return (
    <div>
      <button onClick={openIconPicker}>
        选择图标
      </button>
      
      {selectedIcon && (
        <Icon 
          name={selectedIcon} 
          type={selectedType} 
        />
      )}
      
      <IconPicker
        open={isPickerOpen}
        onOpenChange={closeIconPicker}
        onSelect={selectIcon}
        selectedIcon={selectedIcon}
        selectedType={selectedType}
      />
    </div>
  );
}
```

## 5. 注册自定义图标

可以注册自定义图标到系统中，使其可以通过名称直接使用。

```tsx
import { registerCustomIcon } from '@/lib/utils/icon-registry';
import { IconType } from '@/lib/types/icon';

// 注册 SVG 图标
registerCustomIcon(
  'my-custom-icon',
  IconType.SVG,
  {
    path: '/icons/custom.svg',
    category: 'custom',
    tags: ['custom', 'my', 'icon']
  }
);

// 注册图片图标
registerCustomIcon(
  'my-logo',
  IconType.IMAGE,
  {
    path: '/images/logo.png',
    category: 'custom',
    tags: ['logo', 'brand']
  }
);
```

## 6. 演示组件

系统提供了演示组件，可以查看和测试图标系统的功能。

```tsx
import { IconDemo } from '@/lib/components/ui/view';

function MyPage() {
  return (
    <div>
      <h1>图标系统演示</h1>
      <IconDemo />
    </div>
  );
}
```

## 7. 最佳实践

1. **使用语义化名称**: 选择能够表达图标含义的名称，如 `Home` 而不是 `icon1`。
2. **保持一致性**: 在整个应用中保持图标使用的一致性，避免同一功能使用不同图标。
3. **考虑可访问性**: 为图标添加适当的 `aria-label` 或 `alt` 属性，提高可访问性。
4. **响应式设计**: 根据不同屏幕大小调整图标大小，确保在各种设备上的良好显示。
5. **性能优化**: 对于频繁使用的图标，考虑使用 Lucide 内置图标而不是外部 SVG 或图片。


## 8. 目录结构
lib/
├── types/
│   └── icon.ts              # 图标类型定义
├── utils/
│   ├── icon-registry.ts     # 图标注册表实现
│   └── icon-helpers.ts      # 图标辅助函数
├── hooks/
│   └── useIcon.ts           # 图标相关钩子
├── components/
│   ├── common/
│   │   ├── Icon.tsx         # 统一图标组件
│   │   ├── IconPicker.tsx   # 图标选择器组件
│   │   └── index.ts         # 导出组件
│   └── ui/
│       └── view/
│           └── IconDemo.tsx # 图标演示组件
