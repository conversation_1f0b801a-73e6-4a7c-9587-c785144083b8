import { useState, useEffect, useCallback } from "react"

interface UseFavoriteGameReturn {
	isFavorited: boolean
	handleFavorite: () => void
}

const FAVORITE_COOKIE_NAME = "favorite_games"
const COOKIE_EXPIRY_DAYS = 30

/**
 * 自定义 Hook：管理游戏收藏状态
 * 使用 cookies 持久化收藏状态
 */
export const useFavoriteGame = (gameId: string): UseFavoriteGameReturn => {
	const [isFavorited, setIsFavorited] = useState(false)

	// Cookie 操作工具函数
	const getCookieValue = useCallback((name: string): string => {
		if (typeof window === "undefined") return ""
		const value = `; ${document.cookie}`
		const parts = value.split(`; ${name}=`)
		if (parts.length === 2) return parts.pop()?.split(";").shift() || ""
		return ""
	}, [])

	const setCookie = useCallback((name: string, value: string, days: number): void => {
		if (typeof window === "undefined") return
		const date = new Date()
		date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000)
		const expires = `expires=${date.toUTCString()}`
		document.cookie = `${name}=${value}; ${expires}; path=/`
	}, [])

	// 初始化时从 cookies 中读取收藏状态
	useEffect(() => {
		if (typeof window === "undefined") return

		const favoriteGames = getCookieValue(FAVORITE_COOKIE_NAME)
		if (favoriteGames) {
			try {
				const gamesArray = JSON.parse(favoriteGames)
				setIsFavorited(gamesArray.includes(gameId))
			} catch (e) {
				console.error("解析收藏游戏cookie失败", e)
			}
		}
	}, [gameId, getCookieValue])

	// 切换收藏状态
	const handleFavorite = useCallback(() => {
		if (typeof window === "undefined") return

		// 获取当前收藏的游戏列表
		let favoriteGames: string[] = []
		const favoriteGamesStr = getCookieValue(FAVORITE_COOKIE_NAME)
		if (favoriteGamesStr) {
			try {
				favoriteGames = JSON.parse(favoriteGamesStr)
			} catch (e) {
				console.error("解析收藏游戏cookie失败", e)
			}
		}

		// 切换收藏状态
		if (isFavorited) {
			// 如果已收藏，则移除
			favoriteGames = favoriteGames.filter((id) => id !== gameId)
		} else {
			// 如果未收藏，则添加
			favoriteGames.push(gameId)
		}

		// 更新 cookie
		setCookie(FAVORITE_COOKIE_NAME, JSON.stringify(favoriteGames), COOKIE_EXPIRY_DAYS)

		// 更新状态
		setIsFavorited(!isFavorited)
	}, [gameId, isFavorited, getCookieValue, setCookie])

	return {
		isFavorited,
		handleFavorite,
	}
}
