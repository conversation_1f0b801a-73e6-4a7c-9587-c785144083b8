"use client";

import React from "react";
import { useLocale } from "next-intl";
import { usePathname } from "next/navigation";
import { Link } from "@/lib/i18n/navigation";
import { ChevronDown, ChevronRight, Languages } from "lucide-react";
import { cn } from "@/lib/utils/react/styles"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/lib/components/ui/dropdown-menu";
import { Language } from "@/lib/types";

interface LanguageSelectorProps {
  languages: Record<string, Language>;
}

const flagMap: Record<string, string> = {
  en: "🇺🇸", // 英语 - 美国
  de: "🇩🇪", // 德语 - 德国
  fr: "🇫🇷", // 法语 - 法国
  ja: "🇯🇵", // 日语 - 日本
  ko: "🇰🇷", // 韩语 - 韩国
  "zh-TW": "🇭🇰", // 繁体中文 - 香港
  "es-ES": "🇪🇸", // 西班牙语 - 西班牙
  it: "🇮🇹", // 意大利语 - 意大利
  nl: "🇳🇱", // 荷兰语 - 荷兰
  "pt-PT": "🇵🇹", // 葡萄牙语 - 葡萄牙
};

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  languages,
}) => {
  const locale = useLocale();
  const pathname = usePathname();

  // 去除当前 locale 前缀，特殊情况处理
  const cleanPathname =
    pathname === `/${locale}` ? "/" : pathname.replace(new RegExp(`^/${locale}`), "");

  const currentLang = languages[locale];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className="flex items-center bg-primary/80 text-primary-foreground rounded px-3 py-1 text-sm hover:bg-primary/90 transition-colors"
        >
          <Languages className="mr-2 h-4 w-4" />
          {currentLang?.localName ?? locale}
          <ChevronDown className="ml-2 h-4 w-4" />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className="
          w-56 
          bg-primary/95 
          text-primary-foreground
          dark:bg-primary/80 
          dark:text-primary-foreground
          backdrop-blur-md 
          p-2 
          rounded-md
          border border-border 
          dark:border-border
          shadow-lg
        "
      >
        <div className="flex flex-col gap-1">
          {Object.entries(languages).map(([key, lang]) => (
            <DropdownMenuItem key={key} asChild>
              <Link
                className={cn(
                  "flex items-center gap-2 w-full rounded-md px-2 py-2 transition-colors cursor-pointer rounded-md",
                  locale === key
                    ? "bg-navbar-foreground/10 text-navbar-foreground font-semibold dark:bg-navbar-foreground/20"
                    : "hover:bg-navbar-foreground/10 text-navbar-foreground/80 hover:text-navbar-foreground dark:hover:bg-navbar-foreground/20"
                )}
                href={cleanPathname}
                locale={key}
              >
                <span className="mr-2 text-lg">{flagMap[key] ?? "🏳️"}</span>
                {lang.localName}
              </Link>
            </DropdownMenuItem>
          ))}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default LanguageSelector;
