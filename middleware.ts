import { routing } from "@/lib/i18n/routing"
import createMiddleware from "next-intl/middleware"
import type { NextRequest } from "next/server"

export const config = {
	// Match only internationalized pathnames
	matcher: ['/((?!_next/static|_next/image|style-guide|authorization|favicon.ico).*)'],
}

const handleRouting = createMiddleware(routing)
export default function middleware(request: NextRequest) {
	  const pathname = request.nextUrl.pathname;
	return handleRouting(request)
}
