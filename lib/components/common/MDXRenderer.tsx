'use client';

import React from 'react';
import { MDXRemote, MDXRemoteSerializeResult } from 'next-mdx-remote';
import Image from 'next/image';
import Link from 'next/link';
import { Icon } from '@/lib/components/common';

// Define custom components for MDX
const components = {
  h1: ({children}: {children: React.ReactNode}) => (
    <h1 className="text-3xl font-bold text-gray-900 mb-4 mt-6">{children}</h1>
  ),
  h2: ({children}: {children: React.ReactNode}) => (
    <h2 className="text-2xl font-bold text-gray-900 mb-3 mt-5">{children}</h2>
  ),
  h3: ({children}: {children: React.ReactNode}) => (
    <h3 className="text-xl font-bold text-gray-900 mb-2 mt-4">{children}</h3>
  ),
  p: ({children}: {children: React.ReactNode}) => (
    <p className="text-gray-700 mb-4">{children}</p>
  ),
  a: ({href, children}: {href?: string, children: React.ReactNode}) => (
    <Link href={href || '#'} className="text-indigo-600 hover:text-indigo-800 underline">
      {children}
    </Link>
  ),
  img: ({src, alt}: {src?: string, alt?: string}) => (
    <div className="my-6">
      <Image
        src={src || ''}
        alt={alt || ''}
        width={800}
        height={450}
        className="rounded-lg shadow-md w-full h-auto"
      />
    </div>
  ),
  ul: ({children}: {children: React.ReactNode}) => (
    <ul className="list-disc pl-6 mb-4 space-y-2">{children}</ul>
  ),
  ol: ({children}: {children: React.ReactNode}) => (
    <ol className="list-decimal pl-6 mb-4 space-y-2">{children}</ol>
  ),
  li: ({children}: {children: React.ReactNode}) => (
    <li className="text-gray-700">{children}</li>
  ),
  blockquote: ({children}: {children: React.ReactNode}) => (
    <blockquote className="border-l-4 border-indigo-500 pl-4 italic my-4 text-gray-600">
      {children}
    </blockquote>
  ),
  code: ({children}: {children: React.ReactNode}) => (
    <code className="bg-gray-100 text-gray-800 rounded px-1 py-0.5 font-mono text-sm">
      {children}
    </code>
  ),
  pre: ({children}: {children: React.ReactNode}) => (
    <pre className="bg-gray-100 text-gray-800 rounded p-4 overflow-x-auto my-4 font-mono text-sm">
      {children}
    </pre>
  ),
};

interface MDXRendererProps {
  mdxSource: MDXRemoteSerializeResult;
  className?: string;
}

const MDXRenderer: React.FC<MDXRendererProps> = ({
  mdxSource,
  className = "prose prose-lg max-w-none mb-12"
}) => {
  return (
    <div className={className}>
      <MDXRemote {...mdxSource} components={components} />
    </div>
  );
};

export default MDXRenderer;
