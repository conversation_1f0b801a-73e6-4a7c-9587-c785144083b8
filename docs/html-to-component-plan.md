# public/html 目录静态页面组件化改造方案

## 目标
将 `public/html` 目录下的所有纯静态 HTML 文件内容，按照当前项目的技术栈和规范，提取为可复用的 React 组件，便于后续维护和独立修改各个页面片段。

## 执行思路

### 1. 页面梳理与内容分析
- 遍历 `public/html` 目录下所有 HTML 文件，分析每个页面的结构和内容。
- 识别页面中的通用结构（如头部、导航栏、侧边栏、底部、内容区等）。
- 标记可复用的 UI 区块、功能性区域和独立内容片段。

### 2. 组件拆分与命名
- 按照页面结构，将每个页面拆分为多个语义明确、粒度合适的 React 组件。
- 组件命名遵循项目命名规范，优先采用 PascalCase。
- 通用组件（如 Header、Footer、Sidebar、Card、List 等）优先抽离到 `components` 目录下。
- 页面专属组件可放在对应页面目录下的 `components` 子目录。

### 3. 组件实现规范
- 使用 TypeScript + React 进行组件开发。
- UI 层优先使用 TailwindCSS 和 shadcn/ui，遵循相关规范。
- 组件 props 设计合理，支持内容和样式的灵活传递。
- 静态内容通过 props 传递，避免硬编码。
- 图片、icon 等资源统一管理。
- 支持国际化（i18n），文本内容通过 next-intl 处理。

### 4. 页面重构
- 每个 HTML 页面对应一个 Next.js 页面（如 `app/xxx/page.tsx`）。
- 页面文件中组合已拆分的组件，保持结构清晰。
- 页面级数据获取、SEO metadata 等按项目规范实现。

### 5. 代码组织与目录结构
- 组件统一放在 `components` 目录，按功能/页面分子目录管理。
- 公共样式、资源、类型定义等按项目结构规范组织。
- 保持代码风格一致，遵循 Biome、TS/React 规范。

### 6. 迁移与测试
- 逐步迁移每个页面，确保页面还原度和交互一致性。
- 组件单元测试和页面端到端测试覆盖。

### 7. 交付与文档
- 每个组件和页面需补充必要的注释和使用说明。
- 迁移完成后，更新项目 README 和开发文档。

---

> 本文档为 `public/html` 目录下静态页面组件化改造的整体方案，后续将按此方案逐步推进页面迁移与组件开发。 