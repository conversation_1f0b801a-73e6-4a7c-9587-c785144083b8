/**
 * API客户端工具
 * 用于在构建时获取数据
 * 增加了缓存机制，避免重复请求相同的数据
 */

// 导入API类型定义
import {
	ProjectGame,
	GameCategory,
	GameTag,
	ApiRequestParams,
	SiteSettings,
	NavItem,
	ProjectLocaleSiteSettingType,
	Language,
	MetadataInfo,
	GameLocaleContent,
	GameTagList,
	ArticlePost,
	GameLocaleArray,
} from "@/lib/types"

import { fetchGet } from "@/lib/utils/react/requests"
import cachedSiteSettings from "@/lib/config/siteSettings"

// API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_WEB_API_URL || ""

// 当前数据所属项目Id
const PROJECT_ID = process.env.NEXT_PUBLIC_PROJECT_ID || ""
const isProd = process.env.NODE_ENV === "production"
// 缓存配置(开发环境不启用缓存)
const CACHE_ENABLED = isProd // 是否启用缓存
const CACHE_TTL = 5 * 60 * 1000 // 缓存有效期，默认5分钟

// 缓存存储
interface CacheItem<T> {
	data: T
	timestamp: number
}

// 使用全局缓存
const globalCache =
	(global as any).__API_CACHE__ ?? new Map<string, CacheItem<any>>()
;(global as any).__API_CACHE__ = globalCache
/**
 * 生成缓存键
 * @param endpoint API端点
 * @param params 请求参数
 * @returns 缓存键
 */
function generateCacheKey(endpoint: string, params: ApiRequestParams): string {
	return `${endpoint}:${JSON.stringify(params)}`
}

/**
 * 从缓存中获取数据
 * @param key 缓存键
 * @returns 缓存的数据，如果不存在或已过期则返回null
 */
function getFromCache<T>(key: string): T | null {
	if (!CACHE_ENABLED) return null

	const cached = globalCache.get(key)
	if (!cached) return null

	const now = Date.now()
	if (now - cached.timestamp > CACHE_TTL) {
		// 缓存已过期
		globalCache.delete(key)
		return null
	}

	return cached.data as T
}

/**
 * 将数据存入缓存
 * @param key 缓存键
 * @param data 要缓存的数据
 */
function saveToCache<T>(key: string, data: T): void {
	if (!CACHE_ENABLED) return

	globalCache.set(key, {
		data,
		timestamp: Date.now(),
	})
}

/**
 * 发送API请求
 * @param endpoint API端点
 * @param params 请求参数
 * @param options 请求选项
 * @returns API响应数据
 */
export async function fetchFromApi<T>(
	endpoint: string,
	params: ApiRequestParams = {},
	options: { skipCache?: boolean } = {},
): Promise<T> {
	const locale = params.locale || ""
	const projectId = params.projectId || PROJECT_ID

	// 处理请求参数
	const requestParams: ApiRequestParams = {
		...params,
		locale,
		projectId,
	}

	// 生成缓存键
	const cacheKey = generateCacheKey(endpoint, requestParams)

	// 如果未指定跳过缓存，尝试从缓存获取
	if (!options.skipCache || CACHE_ENABLED) {
		const cachedData = getFromCache<T>(cacheKey)
		if (cachedData) {
			console.log(
				`[Cache Hit] 使用缓存数据访问 ${endpoint},locale:${locale},project_id=${projectId}`,
			)
			return cachedData
		}
	}

	// 构建请求URL
	const url = new URL(`${API_BASE_URL}${endpoint}`)

	// 将参数添加到URL
	Object.entries(requestParams).forEach(([key, value]) => {
		if (value !== undefined) {
			url.searchParams.append(key, String(value))
		}
	})

	try {
		// 发送请求
		console.log(
			`访问接口:${url.toString()}获取数据，locale:${locale},project_id=${projectId}`,
		)

		const response = await fetchGet<T>(url.toString(), {})

		if (!response) {
			throw new Error(`API request failed with status ${response}`)
		}
		// console.log(`访问接口:${url.toString()}API返回数据:`, response)
		// 缓存响应数据
		saveToCache(cacheKey, response)

		return response
	} catch (error) {
		console.error(`Error fetching from API (${endpoint}):`, error)
		throw error
	}
}

/**
 * 获取网站全局配置信息（与语言无关）
 * @param params 其他参数
 * @returns 网站配置信息
 */
export async function getSiteSettings(
	params: ApiRequestParams = {},
): Promise<SiteSettings> {
	// 如果参数中指定了skipCache=true，则跳过配置文件缓存
	// 这主要用于构建脚本中，确保获取最新数据
	if (params.skipCache || !CACHE_ENABLED) {
		return fetchFromApi<SiteSettings>("/site-settings", params, {
			skipCache: true,
		})
	}

	// 如果有预先加载的站点设置，且不是在客户端，则直接返回
	if (cachedSiteSettings?.defaultLocale) {
		console.log("[Config File Hit] 使用配置文件中的站点设置数据")
		return cachedSiteSettings as SiteSettings
	}

	// 否则从API获取
	return fetchFromApi<SiteSettings>("/site-settings", params)
}

/**
 * 根据语言查询网站的配置信息
 * @param locale 语言
 * @param type 类型（metadata, nav, links, categories等）
 * @param params 其他参数
 * @returns 特定类型的网站配置信息
 */
export async function getLocaleSiteSettings(
	locale: string,
): Promise<{ content: any; text: string | null; type: string }[]> {
	return fetchFromApi<{ content: any; text: string | null; type: string }[]>(
		"/locale-site-settings",
		{ locale },
	)
}

/**
 * 获取导航数据
 * @param locale 语言
 * @param params 其他参数
 * @returns 导航数据
 */
export async function getNavItems(locale: string): Promise<NavItem[]> {
	const result = await getLocaleSiteSettings(locale)
	const navItems = result.find(
		(item) => item.type === ProjectLocaleSiteSettingType.Nav,
	)
	return navItems?.content as unknown as NavItem[]
}

/**
 * 获取首页元数据(针对盒子游戏网站的首页)
 * @param locale 语言
 * @param params 其他参数
 * @returns 首页元数据
 */
export async function getHomePageMetadata(
	locale: string,
): Promise<MetadataInfo> {
	const result = await getLocaleSiteSettings(locale)
	const metadata = result.find(
		(item) => item.type === ProjectLocaleSiteSettingType.Metadata,
	)
	return metadata?.content as unknown as MetadataInfo
}

/**
 * 获取指定语言的游戏分类数据
 * @param locale 语言
 * @param params 其他参数
 * @returns 分类数据
 */
export async function getGameCategoriesByLocale(
	locale: string,
): Promise<GameCategory[]> {
	const result = await getLocaleSiteSettings(locale)
	const categories = result.find(
		(item) => item.type === ProjectLocaleSiteSettingType.GameCategories,
	)
	if (categories?.content) {
		return categories?.content as unknown as GameCategory[]
	}
	return []
}
/**
 * 根据语言获取游戏分类详情数据
 * @param locale 语言
 * @param slug 游戏分类slug
 * @returns 游戏分类详情数据
 */
export async function getGameCategoryDetailByLocale(
	locale: string,
	slug: string,
): Promise<GameCategory> {
	// 获取所有语言的标签
	const list = await getGameCategoriesByLocale(locale)
	// 查找指定语言的标签
	const data = list.find((item: GameCategory) => item.slug === slug)
	if (data) {
		return data as unknown as GameCategory
	}

	return {} as GameCategory
}

/**
 * 获取支持的语言列表
 * @returns 支持的语言列表
 */
export async function getLocales(): Promise<string[]> {
	// 调用新接口，但只返回语言列表部分
	const siteSettings = await getSiteSettings()
	return siteSettings.languanges
}

/**
 * 获取语言名称映射
 * @returns 语言名称映射
 */
export async function getLocaleNames(): Promise<Record<string, Language>> {
	// 调用新接口，但只返回语言名称映射部分
	const siteSettings = await getSiteSettings()
	return siteSettings.languangesNames
}

/**
 * 获取默认语言
 * @returns 默认语言
 */
export async function getDefaultLocaleByAPI(): Promise<string> {
	const siteSettings = await getSiteSettings()
	return siteSettings.defaultLocale
}

/**
 * 获取游戏列表数据(包含游戏多语言详情内容)
 * 该接口返回当前项目下的所有游戏，包括游戏的多语言内容
 * @param params 其他参数
 * @returns 游戏列表数据
 */
export async function getGames(): Promise<ProjectGame[]> {
	return fetchFromApi<ProjectGame[]>("/games")
}

/**
 * 获取首页游戏
 * @param params 其他参数
 * @returns 首页游戏
 */
export async function getHomePageGame(): Promise<ProjectGame> {
	const games = await getGames()
	const game = games.find((game) => game.isPrimary)
	if (!game) {
		console.error("没有找到首页游戏=====>", games)
		return {} as ProjectGame
	}
	return game
}

/**
 * 获取某个游戏数据
 * @param locale 语言
 * @param gameId 游戏ID
 * @param params 其他参数
 * @returns 游戏详情数据
 */
export async function getProjectGame(gameId: string): Promise<ProjectGame> {
	const games = await getGames()
	const game = games.find((game) => game.id === gameId)
	if (!game) {
		console.error(`getProjectGame 没有找到游戏:${gameId}`)
		return {} as ProjectGame
	}
	return game
}

/**
 * 根据游戏访问路径获取游戏详情内容
 * @param slug 游戏访问路径
 * @returns 游戏详情内容
 */
export async function getProjectGameBySlug(slug: string): Promise<ProjectGame> {
	//如果slug没有带/games/前缀，则自动添加，首页游戏slug为空
	let path = slug
	if (slug !== "" && !slug.startsWith("/games/")) {
		path = `/games/${slug}`
	}
	if (path === "") {
		return await getHomePageGame()
	}
	const games = await getGames()
	const game = games.find((game) => game.slug === path)
	if (!game) {
		console.error(`getProjectGameBySlug 没有找到游戏:${path}`)
		return {} as ProjectGame
	}
	return game
}

/**
 * 获取游戏元数据(SEO相关信息)
 * @param locale 语言
 * @param projectGame 游戏信息
 * @returns 游戏元数据
 */
export function getProjectGameMetadata(
	locale: string,
	projectGame: ProjectGame,
): MetadataInfo {
	const gameLocale = projectGame.gameLocales?.find(
		(gameLocale) => gameLocale.locale === locale,
	) as unknown as GameLocaleArray
	if (!gameLocale) {
		console.error(`没有找到游戏详情内容:${projectGame.id},locale:${locale}`)
		return {} as MetadataInfo
	}
	return gameLocale.content.metadata
}

/**
 * 获取游戏详情内容
 * @param locale 语言
 * @param projectGame 游戏信息
 * @returns 游戏详情内容
 */
export function getGameLocaleContent(
	locale: string,
	projectGame: ProjectGame,
): GameLocaleContent {
	const gameLocale = projectGame.gameLocales?.find(
		(gameLocale) => gameLocale.locale === locale,
	)
	if (!gameLocale) {
		console.error(`没有找到游戏详情内容:${projectGame.id},locale:${locale}`)
		return {} as GameLocaleContent
	}
	return gameLocale.content
}

/**
 * 获取标签数据（当前项目下的所有标签，从所有游戏的标签中提取）
 * @returns 标签数据(包含所有语言的标签)
 */
export async function getGameTagList(): Promise<GameTagList[]> {
	return fetchFromApi<GameTagList[]>("/game-tag-list")
}

/**
 * 根据语言获取标签数据（从完整的标签列表中筛选出特定语言的标签）
 * @param locale 语言
 * @returns 标签数据
 */
export async function getGameTagsByLocale(locale: string): Promise<GameTag[]> {
	// 获取所有语言的标签
	const tagList = await getGameTagList()

	// 查找指定语言的标签
	const localeData = tagList.find((item: GameTagList) => item.locale === locale)
	if (localeData) {
		return localeData.tags as unknown as GameTag[]
	}

	return []
}

/**
 * 根据标签slug获取标签详情
 * @param locale 语言
 * @param tagSlug 标签slug
 * @returns 标签详情
 */
export async function getGameTagDetailBySlug(
	locale: string,
	tagSlug: string,
): Promise<GameTag> {
	const tagList = await getGameTagsByLocale(locale)
	const tag = tagList.find((item: GameTag) => item.slug === tagSlug)
	if (tag) {
		return tag
	}
	return {} as GameTag
}

/**
 * 根据语言获取所有文章列表
 * @param locale 语言
 * @param params 其他参数
 * @returns 文章列表
 */
export async function getArticleByLocale(
	locale: string,
): Promise<ArticlePost[]> {
	return fetchFromApi<ArticlePost[]>("/articles", { locale })
}

/**
 * 根据语言和slug获取文章详情
 * @param locale 语言
 * @param slug 文章slug
 * @returns 文章详情
 */
export async function getArticleBySlug(
	locale: string,
	slug: string,
): Promise<ArticlePost> {
	const articles = await getArticleByLocale(locale)
	return articles.find(
		(item) => item.slug === slug || item.slug === `/${slug}`,
	) as unknown as ArticlePost
}
