
import { <PERSON>ada<PERSON> } from "next"
import { Breadcrumb } from "@/lib/components/ui/view/Breadcrumb"
import { GameContentContainer } from "@/lib/components/ui/view/GamePageContent"

import {
	getProjectGame,
	getGameLocaleContent,
	getProjectGameBySlug,
	getGames,
} from "@/lib/services/api-client"
import { locales as apiLocales } from "@/lib/i18n/locales"
import { setRequestLocale, getTranslations } from "next-intl/server"
import { enhanceGameMetadata, generateGameStructuredData } from "@/lib/utils/metadata"
import { GameLocaleContent, ProjectGame, BreadcrumbItem } from "@/lib/types"
export const dynamic = 'force-static'
type Props = {
	params: Promise<{ locale: string; slug: string }>
}

export async function generateStaticParams() {
	try {
		// Get all supported locales
		const availableGames: ProjectGame[] = await getGames()
		// Generate all possible combinations of locale and game slug
		const params = []
		if (!availableGames || availableGames.length === 0) {
			console.warn("generateStaticParams: No games found, returning empty params.");
			for (const locale of apiLocales) {
				params.push({
					locale,
					slug: "index"
				})
			}
			return params
		}

		for (const locale of apiLocales) {
			for (const game of availableGames) {
				// 主游戏不在这里处理
				if (!game.isPrimary) {
					// 移除 /games/ 前缀，只保留游戏的实际slug
					const gameSlug = game.slug.replace(/^\/games\//, '')
					if (gameSlug) {
						params.push({
							locale,
							slug: gameSlug,
						})
					}
				}
			}
		}
		const filteredParams = params.filter((item) => item.slug != null && item.slug !== "")
		// 避免返回空数组导致编译出错
		if(filteredParams.length === 0){
			for (const locale of apiLocales) {
				params.push({
					locale,
					slug: "index"
				})
			}
			console.warn("generateStaticParams: No games found, returning empty params.")
			return params
		}
		return filteredParams
	} catch (error) {
		console.error("Failed to fetch params for generateStaticParams:", error)
		return []
	}
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { locale, slug } = await params
	setRequestLocale(locale)
	const gameSlug = decodeURIComponent(slug)
	const projectGame = await getProjectGameBySlug(gameSlug)
	if (!projectGame || !projectGame.id) {
		console.log(`获取游戏信息失败:${gameSlug}`)
		return {
			title: "Game Not Found",
		}
	}
	// 使用增强的元数据函数
	const metadata = await enhanceGameMetadata(gameSlug, projectGame, locale)
	return {
		...metadata,
	}
}

export default async function GamePage({ params }: Props) {
	const { locale, slug } = await params
	// slug中可能包含url编码，需要解码
	const gameSlug = decodeURIComponent(slug)
	const projectGame = await getProjectGameBySlug(gameSlug)
	if (!projectGame || !projectGame.id) {
		console.log(`获取游戏信息失败:${gameSlug}`)
		return (
			<div className="container mx-auto px-4 py-6 text-center">
				<h1 className="text-2xl font-bold mb-4">Game Not Found</h1>
				<p>Sorry, the game you requested does not exist or has been removed.</p>
			</div>
		)
	}
	const gameInfo = await getProjectGame(projectGame.id)
	const t = await getTranslations()
	const gameLocaleContent: GameLocaleContent = await getGameLocaleContent(
		locale,
		gameInfo,
	)

	const breadcrumbItems: BreadcrumbItem[] = [
		{ label: t("Common.Home"), href: "/" },
		{ label: t("Common.GameList"), href: "/games" },
		...(gameLocaleContent.breadcrumbItems || []),
	]
	// 获取结构化数据
	const structuredData = await generateGameStructuredData(gameSlug, gameInfo, locale)

	return (
		<>
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
			/>
			<Breadcrumb items={breadcrumbItems} />
			<main className="container mx-auto px-4 py-6">
				<GameContentContainer gameId={gameInfo.id} locale={locale} />
			</main>
		</>
	)
}
