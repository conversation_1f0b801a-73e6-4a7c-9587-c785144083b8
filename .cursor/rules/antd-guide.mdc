---
description:  Ant Design (antd) 使用指南
globs: 
alwaysApply: false
---
以下为 antd 的核心使用方法和最佳实践总结，并附带官方文档链接以便深入查阅。

---

## 1. 快速上手

- 安装：  
  ```bash
  npm install antd
  ```
- 基本用法：  
  在 React 组件中直接引入并使用 antd 组件。
  ```jsx
  import { Button } from 'antd';

  const App = () => <Button type="primary">Primary Button</Button>;
  ```
  [组件总览与用法](https://ant-design.antgroup.com/components/overview-cn/)

---

## 2. 主题定制（Theme Token）

Ant Design v5 引入了全新的 Design Token 体系，支持动态主题、局部主题、组件级定制等。

- **全局主题定制**  
  通过 `ConfigProvider` 的 `theme` 属性设置全局主题变量（token）。
  ```jsx
  import { ConfigProvider, Button } from 'antd';

  const App = () => (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#00b96b', // 主色
          borderRadius: 2,         // 圆角
          colorBgContainer: '#f6ffed', // 容器背景色
        },
      }}
    >
      <Button type="primary">Primary</Button>
    </ConfigProvider>
  );
  ```
  [定制主题官方文档](https://ant-design.antgroup.com/docs/react/customize-theme-cn)

- **主题算法**  
  支持暗色、紧凑等主题算法，直接切换风格。
  ```jsx
  import { ConfigProvider, theme } from 'antd';

  <ConfigProvider theme={{ algorithm: theme.darkAlgorithm }}>
    {/* ... */}
  </ConfigProvider>
  ```
  [主题算法说明](https://ant-design.antgroup.com/docs/react/customize-theme-cn#%E4%BD%BF%E7%94%A8%E9%A2%84%E8%AE%BE%E7%AE%97%E6%B3%95)

- **组件级定制**  
  针对单个组件定制 token。
  ```jsx
  <ConfigProvider
    theme={{
      components: {
        Button: { colorPrimary: '#00b96b', algorithm: true },
        Input: { colorPrimary: '#eb2f96', algorithm: true },
      },
    }}
  >
    {/* ... */}
  </ConfigProvider>
  ```
  [组件变量定制](https://ant-design.antgroup.com/docs/react/customize-theme-cn#%E4%BF%AE%E6%94%B9%E7%BB%84%E4%BB%B6%E5%8F%98%E9%87%8F)

---

## 3. 通用属性

- **className / style**：所有组件均支持 `className` 和 `style` 属性，便于自定义样式。
- **ref**：大部分组件支持 `ref`，可用于获取 DOM 或组件实例。
- **disabled**：常用交互属性，适用于按钮、输入框等。
- **size**：部分组件支持 `size` 属性（如 `small`、`middle`、`large`）。
- **onChange / onClick**：事件处理函数，遵循 React 事件规范。

[通用属性官方文档](https://ant-design.antgroup.com/docs/react/common-props-cn)

---

## 4. 国际化（i18n）

通过 `ConfigProvider` 的 `locale` 属性设置全局语言环境，支持多语言切换。
```jsx
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

<ConfigProvider locale={zhCN}>
  {/* ... */}
</ConfigProvider>
```
[国际化官方文档](https://ant-design.antgroup.com/docs/react/i18n-cn)

---

## 5. 样式兼容与 CSS 变量

- antd v5 支持 CSS 变量，可通过 `:root` 或 `ConfigProvider` 进行全局/局部样式覆盖。
- 兼容 v4 及更早版本的样式方案，支持平滑迁移。

[样式兼容](https://ant-design.antgroup.com/docs/react/compatible-style-cn)  
[CSS 变量用法](https://ant-design.antgroup.com/docs/react/css-variables-cn)

---

## 6. 服务端渲染（SSR）

- antd 支持 Next.js、Umi 等主流 SSR 框架。
- 推荐使用 `@ant-design/cssinjs` 进行样式收集，避免样式闪烁。

[SSR 官方文档](https://ant-design.antgroup.com/docs/react/server-side-rendering-cn)

---

## 7. 组件常用示例

- **按钮 Button**
  ```jsx
  <Button type="primary">主按钮</Button>
  <Button>默认按钮</Button>
  <Button type="dashed">虚线按钮</Button>
  <Button type="link">链接按钮</Button>
  ```
- **输入框 Input**
  ```jsx
  <Input placeholder="请输入内容" />
  ```
- **表单 Form**
  ```jsx
  import { Form, Input, Button } from 'antd';

  <Form>
    <Form.Item label="用户名" name="username" rules={[{ required: true }]}>
      <Input />
    </Form.Item>
    <Button type="primary" htmlType="submit">提交</Button>
  </Form>
  ```
- **表格 Table**
  ```jsx
  import { Table } from 'antd';

  const columns = [{ title: '姓名', dataIndex: 'name' }];
  const data = [{ key: 1, name: '张三' }];

  <Table columns={columns} dataSource={data} />
  ```

[组件总览与用法](https://ant-design.antgroup.com/components/overview-cn/)

---

## 8. 进阶用法

- **动态切换主题**  
  通过状态管理实时切换主题色。
  ```jsx
  import { ColorPicker, ConfigProvider } from 'antd';
  const [primary, setPrimary] = React.useState('#1677ff');

  <ColorPicker value={primary} onChange={color => setPrimary(color.toHexString())} />
  <ConfigProvider theme={{ token: { colorPrimary: primary } }}>
    {/* ... */}
  </ConfigProvider>
  ```
  [动态切换主题](https://ant-design.antgroup.com/docs/react/customize-theme-cn#%E5%8A%A8%E6%80%81%E5%88%87%E6%8D%A2)

- **局部主题（嵌套主题）**  
  支持嵌套 `ConfigProvider`，实现局部主题覆盖。
  [局部主题说明](https://ant-design.antgroup.com/docs/react/customize-theme-cn#%E5%B1%80%E9%83%A8%E4%B8%BB%E9%A2%98%EF%BC%88%E5%B5%8C%E5%A5%97%E4%B8%BB%E9%A2%98%EF%BC%89)

---

## 9. 参考链接

- [定制主题](https://ant-design.antgroup.com/docs/react/customize-theme-cn)
- [通用属性](https://ant-design.antgroup.com/docs/react/common-props-cn)
- [国际化](https://ant-design.antgroup.com/docs/react/i18n-cn)
- [样式兼容](https://ant-design.antgroup.com/docs/react/compatible-style-cn)
- [CSS 变量](https://ant-design.antgroup.com/docs/react/css-variables-cn)
- [服务端渲染](https://ant-design.antgroup.com/docs/react/server-side-rendering-cn)
- [组件总览](https://ant-design.antgroup.com/components/overview-cn/)

---